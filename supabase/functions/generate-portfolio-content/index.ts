import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { type, data } = await req.json();
    let systemPrompt = '';
    let userPrompt = '';

    if (type === 'epk') {
      systemPrompt = "You are an expert music industry professional who creates compelling Electronic Press Kits (EPKs).";
      userPrompt = `Create a professional EPK for an artist with the following details:
        Name: ${data.artistName}
        Bio: ${data.bio}
        Genre: ${data.genre}
        Please format the content in a structured way that's ready for presentation.`;
    } else if (type === 'pitch') {
      systemPrompt = "You are an expert music industry consultant who creates targeted pitch decks for different stakeholders.";
      userPrompt = `Create a compelling pitch deck content for a ${data.stakeholder} with the following details:
        Key Achievements: ${data.achievements}
        Target Market: ${data.targetMarket}
        Unique Value Proposition: ${data.uniqueValue}
        Please structure the content in a way that's specifically appealing to ${data.stakeholder}s.`;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
      }),
    });

    const result = await response.json();
    console.log('Generated content for:', type);
    
    return new Response(JSON.stringify({ 
      content: result.choices[0].message.content 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error generating portfolio content:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});