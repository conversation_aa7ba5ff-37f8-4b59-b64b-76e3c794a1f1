import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import "https://deno.land/x/xhr@0.1.0/mod.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { genre, targetAudience } = await req.json()

    // Initialize OpenAI
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert music marketing strategist. Generate marketing strategies and content calendars.'
          },
          {
            role: 'user',
            content: `Create a marketing strategy and content calendar for a ${genre} artist targeting ${targetAudience}. Include specific post ideas and timing.`
          }
        ],
      }),
    })

    const aiResponse = await response.json()
    const strategy = aiResponse.choices[0].message.content

    // Generate market predictions based on genre and target audience
    const predictions = {
      potentialReach: Math.floor(Math.random() * 1000000),
      growthRate: (Math.random() * 20).toFixed(1),
      topMarkets: ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany']
        .sort(() => Math.random() - 0.5)
        .slice(0, 3)
    }

    const result = {
      marketingStrategy: {
        overview: strategy.split('\n\n')[0],
        keyPoints: strategy.split('\n').filter(line => line.startsWith('-'))
      },
      contentCalendar: generateContentCalendar(),
      marketPredictions: predictions
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

function generateContentCalendar() {
  const calendar = []
  const platforms = ['Instagram', 'TikTok', 'YouTube', 'Twitter']
  const contentTypes = ['Behind the scenes', 'Performance clip', 'Story/Reel', 'Community engagement']
  
  for (let i = 0; i < 14; i++) {
    const date = new Date()
    date.setDate(date.getDate() + i)
    
    calendar.push({
      date: date.toISOString().split('T')[0],
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      contentType: contentTypes[Math.floor(Math.random() * contentTypes.length)],
      timeSlot: Math.random() > 0.5 ? 'morning' : 'evening'
    })
  }
  
  return calendar
}