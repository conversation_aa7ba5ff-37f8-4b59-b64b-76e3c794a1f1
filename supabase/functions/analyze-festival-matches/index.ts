import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { genres, careerStage, location } = await req.json()
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Fetch festivals that match the criteria
    const { data: festivals, error } = await supabaseClient
      .from('festivals')
      .select('*')
      .contains('genres', genres)
      .contains('career_stages', [careerStage])
      .gte('application_deadline', new Date().toISOString())
      .order('application_deadline', { ascending: true })

    if (error) throw error

    // Calculate match scores and optimal routes
    const matchedFestivals = festivals.map(festival => {
      const genreMatchScore = festival.genres.filter(g => genres.includes(g)).length / genres.length
      const daysUntilDeadline = Math.ceil((new Date(festival.application_deadline) - new Date()) / (1000 * 60 * 60 * 24))
      
      return {
        ...festival,
        matchScore: genreMatchScore * 100,
        daysUntilDeadline,
      }
    })

    // Sort by match score and deadline
    matchedFestivals.sort((a, b) => b.matchScore - a.matchScore || a.daysUntilDeadline - b.daysUntilDeadline)

    return new Response(
      JSON.stringify({ festivals: matchedFestivals }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})