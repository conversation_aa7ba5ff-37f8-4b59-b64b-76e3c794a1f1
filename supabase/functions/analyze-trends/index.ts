import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

const supabase = createClient(supabaseUrl!, supabaseKey!);

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { genre } = await req.json();

    // Get AI insights about the genre
    const aiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a music industry expert analyzing trends. Provide insights about market gaps, opportunities, and style adaptations.'
          },
          {
            role: 'user',
            content: `Analyze the current state and future trajectory of ${genre} music. Include market gaps, opportunities, and style adaptation suggestions.`
          }
        ],
      }),
    });

    const aiData = await aiResponse.json();
    const analysis = aiData.choices[0].message.content;

    // Calculate mock metrics (in a real app, these would come from actual data analysis)
    const popularity = Math.random() * 100;
    const growth = (Math.random() - 0.5) * 20;
    const saturation = Math.random() * 100;

    // Store the analysis in the database
    const { data, error } = await supabase
      .from('music_trends')
      .upsert({
        genre,
        popularity_score: popularity,
        growth_rate: growth,
        market_saturation: saturation,
        predicted_trajectory: { analysis },
        analysis_date: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;

    return new Response(JSON.stringify({ data, analysis }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in analyze-trends function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});