import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { goals, currentSkills, marketTrends } = await req.json();

    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are a career development coach for music industry professionals. Provide specific, actionable advice.'
          },
          {
            role: 'user',
            content: `Based on these goals: ${JSON.stringify(goals)}, current skills: ${JSON.stringify(currentSkills)}, and market trends: ${JSON.stringify(marketTrends)}, provide career development advice and a timeline.`
          }
        ],
      }),
    });

    const aiData = await openAIResponse.json();
    const analysis = aiData.choices[0].message.content;

    // Structure the AI response
    const careerAdvice = {
      recommendations: analysis.split('\n'),
      timeline: generateTimeline(analysis),
      skillGaps: identifySkillGaps(currentSkills, goals),
      networkingSuggestions: generateNetworkingSuggestions(goals)
    };

    return new Response(JSON.stringify(careerAdvice), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

function generateTimeline(analysis: string) {
  // Parse AI response and structure timeline
  const milestones = [];
  const timeframes = ['3 months', '6 months', '1 year', '2 years', '5 years'];
  
  timeframes.forEach(timeframe => {
    milestones.push({
      timeframe,
      goals: [`Achieve ${timeframe} milestone`],
      skills: ['Skill to develop'],
    });
  });
  
  return milestones;
}

function identifySkillGaps(currentSkills: string[], goals: any) {
  return [
    'Music Production',
    'Marketing',
    'Networking',
    'Business Management',
  ].filter(skill => !currentSkills.includes(skill));
}

function generateNetworkingSuggestions(goals: any) {
  return [
    'Connect with local music venues',
    'Join industry associations',
    'Attend music conferences',
    'Participate in online communities',
  ];
}