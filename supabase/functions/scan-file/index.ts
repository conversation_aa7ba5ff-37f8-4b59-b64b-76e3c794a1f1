import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { fileId, contentHash } = await req.json()

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Simulate virus scanning with a delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    // For demo purposes, we'll consider files with content hash ending in '0' as infected
    const scanResult = {
      status: contentHash.endsWith('0') ? 'infected' : 'clean',
      scannedAt: new Date().toISOString(),
      details: contentHash.endsWith('0') 
        ? { threat: 'DEMO-VIRUS', confidence: 0.95 }
        : { threat: null, confidence: 1.0 }
    }

    const { error: updateError } = await supabase
      .from('files')
      .update({
        scan_status: scanResult.status,
        scan_result: scanResult
      })
      .eq('file_path', fileId)

    if (updateError) throw updateError

    return new Response(
      JSON.stringify({ message: 'File scanned successfully', result: scanResult }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
    )
  }
})