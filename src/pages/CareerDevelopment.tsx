import { useState, useEffect } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useNavigate } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import CareerOverview from "./CareerOverview";
import CareerGoals from "./CareerGoals";
import CareerSkills from "./CareerSkills";
import CareerTimeline from "./CareerTimeline";
import CareerNetwork from "./CareerNetwork";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

export default function CareerDevelopment() {
  const session = useSession();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("goals");

  useEffect(() => {
    if (!session) {
      navigate("/auth");
    }
  }, [session, navigate]);

  const { data: careerData, isLoading } = useQuery({
    queryKey: ["career-development", session?.user?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("career_development")
        .select("*")
        .eq("user_id", session?.user?.id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user?.id,
  });

  const updateCareerMutation = useMutation({
    mutationFn: async (newData) => {
      const { data, error } = await supabase
        .from("career_development")
        .upsert({
          user_id: session?.user?.id,
          ...newData,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Your career development plan has been updated.",
      });
    },
  });

  const analyzeCareerPath = async () => {
    try {
      const response = await supabase.functions.invoke("analyze-career-path", {
        body: {
          goals: careerData?.goals || {},
          currentSkills: careerData?.skills_assessment || {},
          marketTrends: {},
        },
      });

      if (response.error) throw response.error;

      await updateCareerMutation.mutateAsync({
        ...careerData,
        career_timeline: response.data.timeline,
        network_suggestions: response.data.networkingSuggestions,
        last_analysis_date: new Date().toISOString(),
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to analyze career path. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container max-w-7xl py-8">
        <div className="space-y-4 animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4" />
          <div className="h-4 bg-muted rounded w-3/4" />
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-40 bg-muted rounded" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-7xl py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Career Development Coach</h1>
        <p className="text-muted-foreground">
          Get personalized guidance to advance your music career
        </p>
      </div>

      <CareerOverview careerData={careerData} />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
        </TabsList>

        <TabsContent value="goals">
          <CareerGoals goals={careerData?.goals} />
        </TabsContent>

        <TabsContent value="skills">
          <CareerSkills skills={careerData?.skills_assessment} />
        </TabsContent>

        <TabsContent value="timeline">
          <CareerTimeline timeline={careerData?.career_timeline} />
        </TabsContent>

        <TabsContent value="network">
          <CareerNetwork suggestions={careerData?.network_suggestions} />
        </TabsContent>
      </Tabs>

      <div className="flex justify-center">
        <Button
          size="lg"
          onClick={analyzeCareerPath}
          disabled={updateCareerMutation.isPending}
        >
          <Rocket className="w-4 h-4 mr-2" />
          Analyze Career Path
        </Button>
      </div>
    </div>
  );
}