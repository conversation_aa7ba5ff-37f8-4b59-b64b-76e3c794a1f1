import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { MapPin, Music, Users, DollarSign, Calendar } from "lucide-react";

const Venues = () => {
  const { toast } = useToast();
  const [capacity, setCapacity] = useState([0, 5000]);
  const [selectedGenre, setSelectedGenre] = useState("");
  const [location, setLocation] = useState("");

  const { data: venues, isLoading } = useQuery({
    queryKey: ["venues", capacity, selectedGenre, location],
    queryFn: async () => {
      const query = supabase
        .from("venues")
        .select("*")
        .gte("capacity", capacity[0])
        .lte("capacity", capacity[1]);

      if (selectedGenre) {
        query.contains("genres", [selectedGenre]);
      }

      if (location) {
        query.ilike("location", `%${location}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });

  const handleBooking = async (venueId: string) => {
    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session?.session?.user) {
        toast({
          title: "Authentication required",
          description: "Please log in to book venues",
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase.from("venue_bookings").insert({
        venue_id: venueId,
        artist_id: session.session.user.id,
        event_date: new Date().toISOString(), // You might want to add a date picker here
      });

      if (error) throw error;

      toast({
        title: "Booking request sent",
        description: "The venue will review your request shortly",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send booking request",
        variant: "destructive",
      });
    }
  };

  const genres = [
    "Rock",
    "Pop",
    "Jazz",
    "Classical",
    "Electronic",
    "Hip Hop",
    "R&B",
    "Country",
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8">Venue Discovery</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="space-y-4">
          <label className="text-sm font-medium">Location</label>
          <Input
            placeholder="Search by city or region"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
          />
        </div>
        
        <div className="space-y-4">
          <label className="text-sm font-medium">Genre</label>
          <Select value={selectedGenre} onValueChange={setSelectedGenre}>
            <SelectTrigger>
              <SelectValue placeholder="Select genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Genres</SelectItem>
              {genres.map((genre) => (
                <SelectItem key={genre} value={genre}>
                  {genre}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-4">
          <label className="text-sm font-medium">Capacity Range</label>
          <Slider
            value={capacity}
            min={0}
            max={5000}
            step={100}
            onValueChange={setCapacity}
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>{capacity[0]} people</span>
            <span>{capacity[1]} people</span>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="h-48 bg-muted rounded-lg mb-4" />
              <div className="h-6 bg-muted rounded w-3/4 mb-2" />
              <div className="h-4 bg-muted rounded w-1/2" />
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {venues?.map((venue) => (
            <Card key={venue.id} className="overflow-hidden">
              <div className="aspect-video bg-muted" />
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">{venue.name}</h3>
                <p className="text-muted-foreground mb-4 line-clamp-2">
                  {venue.description}
                </p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="w-4 h-4" />
                    <span>{venue.location}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="w-4 h-4" />
                    <span>Capacity: {venue.capacity}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Music className="w-4 h-4" />
                    <span>{venue.genres?.join(", ")}</span>
                  </div>
                  {venue.booking_fee && (
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="w-4 h-4" />
                      <span>Booking fee: ${venue.booking_fee}</span>
                    </div>
                  )}
                </div>
                
                <Button
                  className="w-full"
                  onClick={() => handleBooking(venue.id)}
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Request Booking
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default Venues;