import React from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useNavigate } from "react-router-dom";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EPKGenerator } from "@/components/portfolio/EPKGenerator";
import { PerformanceAnalytics } from "@/components/portfolio/PerformanceAnalytics";
import { PitchDeckGenerator } from "@/components/portfolio/PitchDeckGenerator";

const Portfolio = () => {
  const session = useSession();
  const navigate = useNavigate();

  React.useEffect(() => {
    if (!session) {
      navigate("/auth");
    }
  }, [session, navigate]);

  return (
    <div className="container max-w-7xl py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Smart Music Portfolio</h1>
          <p className="text-muted-foreground mt-2">
            Generate professional materials to showcase your music career
          </p>
        </div>
      </div>

      <Tabs defaultValue="epk" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="epk">Electronic Press Kit</TabsTrigger>
          <TabsTrigger value="analytics">Performance Analytics</TabsTrigger>
          <TabsTrigger value="pitch">Pitch Deck Generator</TabsTrigger>
        </TabsList>
        <TabsContent value="epk" className="space-y-4">
          <EPKGenerator />
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <PerformanceAnalytics />
        </TabsContent>
        <TabsContent value="pitch" className="space-y-4">
          <PitchDeckGenerator />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Portfolio;