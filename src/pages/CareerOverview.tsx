import React from "react";
import { Card } from "@/components/ui/card";
import { GraduationCap, Target, Rocket, Network, Calendar } from "lucide-react";

const CareerOverview = ({ careerData }) => {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      <Card className="p-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <Target className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold">Goals Set</h3>
            <p className="text-2xl font-bold">
              {Object.keys(careerData?.goals || {}).length}
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <GraduationCap className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold">Skills</h3>
            <p className="text-2xl font-bold">
              {Object.keys(careerData?.skills_assessment || {}).length}
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <Calendar className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold">Timeline</h3>
            <p className="text-2xl font-bold">
              {(careerData?.career_timeline || []).length} milestones
            </p>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <Network className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold">Network</h3>
            <p className="text-2xl font-bold">
              {(careerData?.network_suggestions || []).length} suggestions
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CareerOverview;