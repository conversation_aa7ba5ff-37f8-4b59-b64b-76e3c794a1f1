import { useState } from "react"
import { useSession } from "@supabase/auth-helpers-react"
import { useQuery } from "@tanstack/react-query"
import { ChartBar, Users, Target, Calendar, Megaphone } from "lucide-react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { supabase } from "@/integrations/supabase/client"

export default function AudienceGrowth() {
  const session = useSession()
  const { toast } = useToast()
  const [genre, setGenre] = useState("")
  const [targetAudience, setTargetAudience] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)

  const { data: strategy, refetch } = useQuery({
    queryKey: ["audienceStrategy", session?.user?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("audience_strategies")
        .select("*")
        .eq("user_id", session?.user?.id)
        .single()

      if (error) throw error
      return data
    },
    enabled: !!session?.user?.id
  })

  const generateStrategy = async () => {
    if (!genre || !targetAudience) {
      toast({
        title: "Missing Information",
        description: "Please provide both genre and target audience",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    try {
      const response = await supabase.functions.invoke("generate-audience-strategy", {
        body: { genre, targetAudience }
      })

      if (response.error) throw response.error

      const { data, error } = await supabase
        .from("audience_strategies")
        .upsert({
          user_id: session?.user?.id,
          genre,
          target_audience: { description: targetAudience },
          marketing_strategy: response.data.marketingStrategy,
          content_calendar: response.data.contentCalendar,
          market_predictions: response.data.marketPredictions
        })
        .select()
        .single()

      if (error) throw error

      toast({
        title: "Strategy Generated",
        description: "Your audience growth strategy has been created"
      })

      refetch()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate strategy. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Audience Growth Strategist</h1>

      <div className="grid gap-6 mb-8">
        <Card className="p-6">
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <label className="text-sm font-medium">Genre</label>
                <Input
                  placeholder="e.g., Hip Hop, Rock, Pop"
                  value={genre}
                  onChange={(e) => setGenre(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Target Audience</label>
                <Input
                  placeholder="e.g., 18-24 year olds interested in urban culture"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                />
              </div>
            </div>
            <Button 
              onClick={generateStrategy} 
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? "Generating..." : "Generate Strategy"}
            </Button>
          </div>
        </Card>

        {strategy && (
          <Tabs defaultValue="strategy" className="space-y-4">
            <TabsList>
              <TabsTrigger value="strategy">
                <Target className="w-4 h-4 mr-2" />
                Strategy
              </TabsTrigger>
              <TabsTrigger value="calendar">
                <Calendar className="w-4 h-4 mr-2" />
                Content Calendar
              </TabsTrigger>
              <TabsTrigger value="predictions">
                <ChartBar className="w-4 h-4 mr-2" />
                Market Predictions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="strategy" className="space-y-4">
              <Card className="p-6">
                <h3 className="text-xl font-semibold mb-4">Marketing Strategy</h3>
                <div className="space-y-4">
                  <p>{strategy.marketing_strategy.overview}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {strategy.marketing_strategy.keyPoints?.map((point: string, index: number) => (
                      <li key={index}>{point}</li>
                    ))}
                  </ul>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="calendar" className="space-y-4">
              <Card className="p-6">
                <h3 className="text-xl font-semibold mb-4">Content Calendar</h3>
                <div className="space-y-4">
                  {strategy.content_calendar.map((item: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{item.date}</p>
                        <p className="text-sm text-muted-foreground">{item.contentType}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{item.platform}</p>
                        <p className="text-sm text-muted-foreground">{item.timeSlot}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="predictions" className="space-y-4">
              <Card className="p-6">
                <h3 className="text-xl font-semibold mb-4">Market Predictions</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Potential Reach</p>
                    <p className="text-2xl font-bold">
                      {strategy.market_predictions.potentialReach.toLocaleString()}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Growth Rate</p>
                    <p className="text-2xl font-bold">
                      {strategy.market_predictions.growthRate}%
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Top Markets</p>
                    <ul className="space-y-1">
                      {strategy.market_predictions.topMarkets.map((market: string, index: number) => (
                        <li key={index}>{market}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  )
}