const Terms = () => {
  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-4xl font-bold mb-8">Terms of Service</h1>
      <div className="prose max-w-none">
        <h2 className="text-2xl font-semibold mt-8 mb-4">1. Acceptance of Terms</h2>
        <p className="mb-4">
          By accessing and using Quincy.AI, you agree to be bound by these Terms of Service and all applicable laws and regulations.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">2. Use License</h2>
        <p className="mb-4">
          Quincy.AI grants you a limited, non-exclusive, non-transferable license to access and use our services for your personal or professional music industry activities.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">3. AI Services</h2>
        <p className="mb-4">
          Our AI-powered features are provided "as is" and we make no warranties about the accuracy or reliability of AI-generated suggestions and matches.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">4. User Content</h2>
        <p className="mb-4">
          You retain all rights to your content while granting us the necessary licenses to provide our services and improve our AI systems.
        </p>
      </div>
    </div>
  );
};

export default Terms;