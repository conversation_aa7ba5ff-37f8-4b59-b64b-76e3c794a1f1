import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MatchingPreferences } from "@/components/matching/MatchingPreferences";
import { MatchingSuggestions } from "@/components/matching/MatchingSuggestions";
import { MatchingHistory } from "@/components/matching/MatchingHistory";
import { Building, Music, Users } from "lucide-react";

export default function Matching() {
  const session = useSession();

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p>Please sign in to access the matching engine.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-4xl font-bold">AI Talent Matching Engine</h1>
        <p className="text-muted-foreground">
          Find your perfect match in the music industry
        </p>
      </div>

      <Tabs defaultValue="suggestions" className="space-y-6">
        <TabsList>
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="history">Match History</TabsTrigger>
        </TabsList>

        <TabsContent value="suggestions" className="space-y-6">
          <MatchingSuggestions />
        </TabsContent>

        <TabsContent value="preferences">
          <MatchingPreferences />
        </TabsContent>

        <TabsContent value="history">
          <MatchingHistory />
        </TabsContent>
      </Tabs>
    </div>
  );
}