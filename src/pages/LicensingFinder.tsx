import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LicensingCatalog } from "@/components/licensing/LicensingCatalog";
import { LicensingMatches } from "@/components/licensing/LicensingMatches";
import { RoyaltyTracking } from "@/components/licensing/RoyaltyTracking";
import { Film, Tv, DollarSign } from "lucide-react";

export default function LicensingFinder() {
  const session = useSession();

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <div className="p-6">
            <p>Please sign in to access the Licensing Opportunity Finder.</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-4xl font-bold">Licensing Opportunity Finder</h1>
        <p className="text-muted-foreground">
          Find and manage licensing opportunities for your music
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-6">
          <Film className="w-8 h-8 text-primary mb-4" />
          <h3 className="text-lg font-semibold mb-2">Film & TV</h3>
          <p className="text-sm text-muted-foreground">
            Match your songs with upcoming film and TV productions
          </p>
        </Card>

        <Card className="p-6">
          <Tv className="w-8 h-8 text-primary mb-4" />
          <h3 className="text-lg font-semibold mb-2">Advertising</h3>
          <p className="text-sm text-muted-foreground">
            Connect with brands looking for the perfect soundtrack
          </p>
        </Card>

        <Card className="p-6">
          <DollarSign className="w-8 h-8 text-primary mb-4" />
          <h3 className="text-lg font-semibold mb-2">Royalty Management</h3>
          <p className="text-sm text-muted-foreground">
            Track and manage your licensing revenue
          </p>
        </Card>
      </div>

      <Tabs defaultValue="catalog" className="space-y-6">
        <TabsList>
          <TabsTrigger value="catalog">Music Catalog</TabsTrigger>
          <TabsTrigger value="matches">Potential Matches</TabsTrigger>
          <TabsTrigger value="royalties">Royalty Tracking</TabsTrigger>
        </TabsList>

        <TabsContent value="catalog">
          <LicensingCatalog />
        </TabsContent>

        <TabsContent value="matches">
          <LicensingMatches />
        </TabsContent>

        <TabsContent value="royalties">
          <RoyaltyTracking />
        </TabsContent>
      </Tabs>
    </div>
  );
}