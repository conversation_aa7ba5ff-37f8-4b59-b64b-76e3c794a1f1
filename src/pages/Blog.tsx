import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const Blog = () => {
  const posts = [
    {
      title: "AI Revolution in Music Production",
      excerpt: "Discover how artificial intelligence is transforming the way music is produced, mixed, and mastered in 2024.",
      date: "2024-02-20",
      author: "<PERSON>",
      category: "Technology"
    },
    {
      title: "Global Collaboration in the Digital Age",
      excerpt: "How AI-powered translation and cultural understanding are breaking down barriers in international music collaboration.",
      date: "2024-02-15",
      author: "<PERSON>",
      category: "Industry Trends"
    },
    {
      title: "The Future of Music Industry Networking",
      excerpt: "Exploring how AI matchmaking is revolutionizing how artists connect with labels, venues, and festivals.",
      date: "2024-02-10",
      author: "<PERSON>",
      category: "Career Development"
    }
  ];

  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-4xl font-bold mb-8">Blog</h1>
      <div className="grid gap-8">
        {posts.map((post, index) => (
          <article key={index} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center gap-2 text-sm text-gray-500 mb-3">
              <span>{post.category}</span>
              <span>•</span>
              <span>{post.author}</span>
            </div>
            <h2 className="text-2xl font-semibold mb-2">{post.title}</h2>
            <p className="text-gray-600 mb-4">{post.excerpt}</p>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">{new Date(post.date).toLocaleDateString()}</span>
              <Button variant="outline" asChild>
                <Link to="#">Read More</Link>
              </Button>
            </div>
          </article>
        ))}
      </div>
    </div>
  );
};

export default Blog;