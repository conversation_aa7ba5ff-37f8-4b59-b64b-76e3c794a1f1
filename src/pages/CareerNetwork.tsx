import React from "react";
import { Card } from "@/components/ui/card";
import { Network } from "lucide-react";

const CareerNetwork = ({ suggestions }) => {
  return (
    <Card className="p-6">
      <h3 className="text-xl font-semibold mb-4">Networking Suggestions</h3>
      <div className="space-y-4">
        {suggestions.map((suggestion, index) => (
          <div key={index} className="flex items-center gap-4 p-4 rounded-lg bg-muted/50">
            <Network className="w-5 h-5 text-primary" />
            <span>{suggestion}</span>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CareerNetwork;