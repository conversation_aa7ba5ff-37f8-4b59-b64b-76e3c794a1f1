import React from "react";
import { Card } from "@/components/ui/card";

const CareerTimeline = ({ timeline }) => {
  return (
    <Card className="p-6">
      <h3 className="text-xl font-semibold mb-4">Career Timeline</h3>
      <div className="space-y-8">
        {timeline.map((milestone, index) => (
          <div key={index} className="relative pl-8 pb-8 border-l-2 border-primary/20 last:pb-0">
            <div className="absolute left-0 top-0 -translate-x-1/2 w-4 h-4 rounded-full bg-primary" />
            <h4 className="font-semibold">{milestone.timeframe}</h4>
            <ul className="mt-2 space-y-2">
              {milestone.goals.map((goal, i) => (
                <li key={i} className="text-muted-foreground">{goal}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CareerTimeline;