import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Trend<PERSON>hart } from "@/components/trends/TrendChart";
import { TrendInsights } from "@/components/trends/TrendInsights";
import { TrendAnalysis } from "@/components/trends/TrendAnalysis";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Search } from "lucide-react";

const Trends = () => {
  const [genre, setGenre] = useState("");
  const { toast } = useToast();

  const { data: trends, isLoading } = useQuery({
    queryKey: ["trends"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("music_trends")
        .select("*")
        .order("analysis_date", { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  const analyzeTrend = async () => {
    if (!genre) {
      toast({
        title: "Genre required",
        description: "Please enter a genre to analyze",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await supabase.functions.invoke("analyze-trends", {
        body: { genre },
      });

      if (response.error) throw response.error;

      toast({
        title: "Analysis Complete",
        description: "New trend analysis has been generated",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to analyze trend",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8">Music Trend Prediction Engine</h1>
      
      <div className="flex gap-4 mb-8">
        <Input
          placeholder="Enter a music genre..."
          value={genre}
          onChange={(e) => setGenre(e.target.value)}
          className="max-w-md"
        />
        <Button onClick={analyzeTrend}>
          <Search className="w-4 h-4 mr-2" />
          Analyze Trend
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <TrendChart data={trends || []} isLoading={isLoading} />
        <TrendInsights data={trends || []} isLoading={isLoading} />
      </div>

      <TrendAnalysis data={trends || []} isLoading={isLoading} />
    </div>
  );
};

export default Trends;