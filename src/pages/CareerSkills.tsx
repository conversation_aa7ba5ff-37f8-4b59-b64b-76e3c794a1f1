import React from "react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

const CareerSkills = ({ skills }) => {
  return (
    <Card className="p-6">
      <h3 className="text-xl font-semibold mb-4">Skills Assessment</h3>
      <div className="space-y-4">
        {Object.entries(skills || {}).map(([skill, level]) => (
          <div key={skill}>
            <div className="flex justify-between mb-2">
              <span>{skill}</span>
              <span>{level}/10</span>
            </div>
            <Progress value={Number(level) * 10} />
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CareerSkills;