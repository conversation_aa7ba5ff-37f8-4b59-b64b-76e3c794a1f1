import React from "react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

const CareerGoals = ({ goals }) => {
  return (
    <Card className="p-6">
      <h3 className="text-xl font-semibold mb-4">Career Goals</h3>
      <div className="space-y-4">
        {Object.entries(goals || {}).map(([goal, progress]) => (
          <div key={goal}>
            <div className="flex justify-between mb-2">
              <span>{goal}</span>
              <span>{progress}%</span>
            </div>
            <Progress value={Number(progress)} />
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CareerGoals;