import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import { useProfile, useUpdateProfile, useUploadProfileImage } from "@/hooks/useProfile";
import { useSession } from "@supabase/auth-helpers-react";
import { ProfileHeader } from "@/components/profile/ProfileHeader";
import { ProfileCategories } from "@/components/profile/ProfileCategories";
import type { Profile } from "@/types/profile";

const Profile = () => {
  const navigate = useNavigate();
  const session = useSession();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  
  const { data: profile, isLoading } = useProfile();
  const updateProfile = useUpdateProfile();
  const uploadProfileImage = useUploadProfileImage();

  const [formData, setFormData] = useState<Partial<Profile>>({
    username: "",
    full_name: "",
    introduction: "",
    primary_role: null,
    secondary_roles: [],
    industry_sectors: [],
    specializations: [],
    regions: [],
    languages: [],
  });

  useEffect(() => {
    if (!session) {
      navigate("/auth");
      return;
    }
    
    if (profile) {
      setFormData({
        username: profile.username || "",
        full_name: profile.full_name || "",
        introduction: profile.introduction || "",
        primary_role: profile.primary_role || null,
        secondary_roles: profile.secondary_roles || [],
        industry_sectors: profile.industry_sectors || [],
        specializations: profile.specializations || [],
        regions: profile.regions || [],
        languages: profile.languages || [],
      });
    }
  }, [profile, session, navigate]);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>, type: 'avatar' | 'cover') => {
    try {
      setUploading(true);
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("You must select an image to upload.");
      }

      const file = event.target.files[0];
      
      if (type === 'avatar') {
        await uploadProfileImage.mutateAsync({ file, isPrimary: true });
      } else {
        const fileExt = file.name.split(".").pop();
        const filePath = `${session?.user.id}-${Math.random()}.${fileExt}`;

        const { error: uploadError } = await supabase.storage
          .from("avatars")
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from("avatars")
          .getPublicUrl(filePath);

        updateProfile.mutate({ cover_photo_url: publicUrl });
      }
    } catch (error: any) {
      toast({
        title: "Error uploading image",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    updateProfile.mutate(formData);
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate("/auth");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Profile</h1>
          <Button onClick={handleSignOut} variant="outline">Sign Out</Button>
        </div>

        {profile && (
          <ProfileHeader
            profile={profile}
            onImageUpload={handleImageUpload}
            uploading={uploading}
          />
        )}

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-1">Username</label>
            <Input
              value={formData.username || ''}
              onChange={(e) => handleFieldChange('username', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Full Name</label>
            <Input
              value={formData.full_name || ''}
              onChange={(e) => handleFieldChange('full_name', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Introduction</label>
            <Textarea
              value={formData.introduction || ''}
              onChange={(e) => handleFieldChange('introduction', e.target.value)}
              rows={4}
            />
          </div>

          <ProfileCategories
            primaryRole={formData.primary_role}
            secondaryRoles={formData.secondary_roles}
            industrySectors={formData.industry_sectors}
            specializations={formData.specializations}
            regions={formData.regions}
            languages={formData.languages}
            onChange={handleFieldChange}
          />

          <Button 
            onClick={handleSubmit} 
            disabled={updateProfile.isPending}
            className="w-full"
          >
            {updateProfile.isPending ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Update Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Profile;