import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { useSession } from "@supabase/auth-helpers-react"
import { Calendar, MapPin, Music, Target, Trophy } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { supabase } from "@/integrations/supabase/client"

const careerStages = ["Emerging", "Developing", "Established", "Professional"]
const genres = ["Rock", "Pop", "Jazz", "Electronic", "Hip Hop", "Classical", "Folk", "World"]

export default function FestivalMatcher() {
  const { toast } = useToast()
  const session = useSession()
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [careerStage, setCareerStage] = useState("")

  const { data: festivalMatches, isLoading } = useQuery({
    queryKey: ["festivals", selectedGenres, careerStage],
    queryFn: async () => {
      if (!selectedGenres.length || !careerStage) return []
      
      const { data } = await supabase.functions.invoke("analyze-festival-matches", {
        body: { genres: selectedGenres, careerStage, location: null }
      })
      return data.festivals
    },
    enabled: !!selectedGenres.length && !!careerStage
  })

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const handleApply = async (festivalId: string) => {
    if (!session) {
      toast({
        title: "Authentication required",
        description: "Please sign in to apply for festivals",
        variant: "destructive"
      })
      return
    }

    try {
      const { error } = await supabase
        .from("festival_applications")
        .insert({ festival_id: festivalId, artist_id: session.user.id })

      if (error) throw error

      toast({
        title: "Application submitted",
        description: "Your festival application has been submitted successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit application. Please try again.",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">Festival Matcher</h1>
        
        <div className="grid gap-6 mb-8">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Your Profile</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Career Stage</label>
                <Select value={careerStage} onValueChange={setCareerStage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your career stage" />
                  </SelectTrigger>
                  <SelectContent>
                    {careerStages.map(stage => (
                      <SelectItem key={stage} value={stage}>{stage}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Genres</label>
                <div className="flex flex-wrap gap-2">
                  {genres.map(genre => (
                    <Button
                      key={genre}
                      variant={selectedGenres.includes(genre) ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleGenreToggle(genre)}
                    >
                      {genre}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <Card key={i} className="p-6 animate-pulse">
                  <div className="h-6 bg-muted rounded w-1/4 mb-4" />
                  <div className="h-4 bg-muted rounded w-3/4 mb-2" />
                  <div className="h-4 bg-muted rounded w-1/2" />
                </Card>
              ))}
            </div>
          ) : festivalMatches?.length ? (
            <div className="space-y-4">
              {festivalMatches.map(festival => (
                <Card key={festival.id} className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">{festival.name}</h3>
                      <p className="text-muted-foreground">{festival.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-primary">
                        {festival.matchScore.toFixed(0)}% Match
                      </div>
                      <Progress value={festival.matchScore} className="w-24" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      <span>{festival.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(festival.start_date).toLocaleDateString()} - {new Date(festival.end_date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Music className="w-4 h-4" />
                      <span>{festival.genres.join(", ")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Trophy className="w-4 h-4" />
                      <span>{festival.career_stages.join(", ")}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Application Deadline: {new Date(festival.application_deadline).toLocaleDateString()}
                      {festival.daysUntilDeadline > 0 && ` (${festival.daysUntilDeadline} days left)`}
                    </div>
                    <Button onClick={() => handleApply(festival.id)}>
                      Apply Now
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          ) : selectedGenres.length && careerStage ? (
            <Card className="p-6 text-center">
              <p className="text-muted-foreground">No matching festivals found. Try adjusting your criteria.</p>
            </Card>
          ) : null}
        </div>
      </div>
    </div>
  )
}