const HelpCenter = () => {
  const faqs = [
    {
      question: "How does AI matching work?",
      answer: "Our AI matching system analyzes your profile, music style, and career goals to connect you with relevant opportunities and collaborators in the music industry."
    },
    {
      question: "What subscription plans are available?",
      answer: "We offer various plans from Free to Scale, each designed to support different stages of your music career. Visit our Pricing page for detailed information."
    },
    {
      question: "How can I get started with Quincy.AI?",
      answer: "Sign up for an account, complete your profile, and start exploring our AI-powered features. Our platform will guide you through the process."
    }
  ];

  return (
    <div className="container mx-auto px-4 py-16">
      <h1 className="text-4xl font-bold mb-8">Help Center</h1>
      <div className="max-w-3xl mx-auto">
        <div className="space-y-8">
          {faqs.map((faq, index) => (
            <div key={index} className="border-b pb-6">
              <h3 className="text-xl font-semibold mb-2">{faq.question}</h3>
              <p className="text-gray-600">{faq.answer}</p>
            </div>
          ))}
        </div>
        
        <div className="mt-12 p-6 bg-gray-50 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Need More Help?</h2>
          <p className="text-gray-600 mb-4">
            Our support team is available 24/7 to assist you with any questions or concerns.
          </p>
          <a href="/contact" className="text-primary hover:underline">
            Contact Support →
          </a>
        </div>
      </div>
    </div>
  );
};

export default HelpCenter;