import { <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { 
  Sparkles, BarChart2, Globe, Music, FileEdit, 
  Building, FileText, MessageSquare, Users, Brain,
  Film, Calendar, GraduationCap
} from "lucide-react";

export default function AIFeatures() {
  const features = [
    {
      icon: <Sparkles className="w-6 h-6" />,
      title: "AI Talent Matching",
      description: "Find your perfect match in the music industry using AI",
      link: "/matching"
    },
    {
      icon: <BarChart2 className="w-6 h-6" />,
      title: "Trend Prediction",
      description: "Analyze global music trends and predict emerging genres",
      link: "/trends"
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: "Cultural Collaboration",
      description: "Connect with artists globally with AI-powered translation",
      link: "/collaboration"
    },
    {
      icon: <Music className="w-6 h-6" />,
      title: "AI Production Advisor",
      description: "Get AI-powered mixing, mastering, and arrangement feedback",
      link: "/production"
    },
    {
      icon: <FileEdit className="w-6 h-6" />,
      title: "Smart Portfolio",
      description: "Generate AI-powered EPKs and pitch decks",
      link: "/portfolio"
    },
    {
      icon: <Building className="w-6 h-6" />,
      title: "Venue Discovery",
      description: "Find and book the perfect venues for your performances",
      link: "/venues"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Audience Growth",
      description: "AI-powered marketing strategies and audience analytics",
      link: "/audience-growth"
    },
    {
      icon: <Film className="w-6 h-6" />,
      title: "Licensing Finder",
      description: "Match your music with film, TV, and advertising opportunities",
      link: "/licensing"
    },
    {
      icon: <Calendar className="w-6 h-6" />,
      title: "Festival Matcher",
      description: "Find and apply to festivals that match your profile",
      link: "/festival-matcher"
    },
    {
      icon: <GraduationCap className="w-6 h-6" />,
      title: "Career Development Coach",
      description: "Get AI-powered guidance to advance your music career",
      link: "/career-development"
    }
  ];

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">AI-Powered Features</h1>
        <p className="text-lg text-muted-foreground">
          Discover all the ways our AI can help grow your music career
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature, index) => (
          <Link key={index} to={feature.link}>
            <Card className="p-6 h-full hover:shadow-lg transition-shadow">
              <div className="flex items-start space-x-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-semibold mb-2">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {feature.description}
                  </p>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}