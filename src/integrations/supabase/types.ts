export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      artist_preferences: {
        Row: {
          career_stage: string | null
          collaboration_interests: string[] | null
          created_at: string
          genres: string[] | null
          id: string
          target_labels: string[] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          career_stage?: string | null
          collaboration_interests?: string[] | null
          created_at?: string
          genres?: string[] | null
          id?: string
          target_labels?: string[] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          career_stage?: string | null
          collaboration_interests?: string[] | null
          created_at?: string
          genres?: string[] | null
          id?: string
          target_labels?: string[] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      audience_strategies: {
        Row: {
          content_calendar: Json | null
          created_at: string
          genre: string
          id: string
          market_predictions: Json | null
          marketing_strategy: Json | null
          target_audience: Json | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content_calendar?: Json | null
          created_at?: string
          genre: string
          id?: string
          market_predictions?: Json | null
          marketing_strategy?: Json | null
          target_audience?: Json | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content_calendar?: Json | null
          created_at?: string
          genre?: string
          id?: string
          market_predictions?: Json | null
          marketing_strategy?: Json | null
          target_audience?: Json | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      career_development: {
        Row: {
          career_timeline: Json | null
          created_at: string | null
          goals: Json | null
          id: string
          last_analysis_date: string | null
          network_suggestions: Json | null
          skills_assessment: Json | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          career_timeline?: Json | null
          created_at?: string | null
          goals?: Json | null
          id?: string
          last_analysis_date?: string | null
          network_suggestions?: Json | null
          skills_assessment?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          career_timeline?: Json | null
          created_at?: string | null
          goals?: Json | null
          id?: string
          last_analysis_date?: string | null
          network_suggestions?: Json | null
          skills_assessment?: Json | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      cultural_collaborations: {
        Row: {
          collaboration_style: string | null
          created_at: string
          cultural_preferences: Json | null
          id: string
          musical_traditions: string[] | null
          preferred_languages: string[] | null
          translation_settings: Json | null
          updated_at: string
          user_id: string
        }
        Insert: {
          collaboration_style?: string | null
          created_at?: string
          cultural_preferences?: Json | null
          id?: string
          musical_traditions?: string[] | null
          preferred_languages?: string[] | null
          translation_settings?: Json | null
          updated_at?: string
          user_id: string
        }
        Update: {
          collaboration_style?: string | null
          created_at?: string
          cultural_preferences?: Json | null
          id?: string
          musical_traditions?: string[] | null
          preferred_languages?: string[] | null
          translation_settings?: Json | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      festival_applications: {
        Row: {
          artist_id: string | null
          festival_id: string | null
          id: string
          status: string | null
          submitted_at: string | null
          updated_at: string | null
        }
        Insert: {
          artist_id?: string | null
          festival_id?: string | null
          id?: string
          status?: string | null
          submitted_at?: string | null
          updated_at?: string | null
        }
        Update: {
          artist_id?: string | null
          festival_id?: string | null
          id?: string
          status?: string | null
          submitted_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "festival_applications_festival_id_fkey"
            columns: ["festival_id"]
            isOneToOne: false
            referencedRelation: "festivals"
            referencedColumns: ["id"]
          },
        ]
      }
      festivals: {
        Row: {
          application_deadline: string | null
          capacity: number | null
          career_stages: string[] | null
          created_at: string | null
          description: string | null
          end_date: string
          genres: string[] | null
          id: string
          location: string
          name: string
          requirements: Json | null
          start_date: string
          updated_at: string | null
        }
        Insert: {
          application_deadline?: string | null
          capacity?: number | null
          career_stages?: string[] | null
          created_at?: string | null
          description?: string | null
          end_date: string
          genres?: string[] | null
          id?: string
          location: string
          name: string
          requirements?: Json | null
          start_date: string
          updated_at?: string | null
        }
        Update: {
          application_deadline?: string | null
          capacity?: number | null
          career_stages?: string[] | null
          created_at?: string | null
          description?: string | null
          end_date?: string
          genres?: string[] | null
          id?: string
          location?: string
          name?: string
          requirements?: Json | null
          start_date?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      file_shares: {
        Row: {
          created_at: string
          created_by: string | null
          file_id: string | null
          id: string
          permission_level: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          file_id?: string | null
          id?: string
          permission_level?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          file_id?: string | null
          id?: string
          permission_level?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "file_shares_file_id_fkey"
            columns: ["file_id"]
            isOneToOne: false
            referencedRelation: "files"
            referencedColumns: ["id"]
          },
        ]
      }
      files: {
        Row: {
          content_hash: string | null
          content_type: string
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          description: string | null
          encryption_metadata: Json | null
          file_path: string
          file_type: string
          filename: string
          folder_path: string | null
          id: string
          is_encrypted: boolean | null
          metadata: Json | null
          previous_versions: Json | null
          scan_result: Json | null
          scan_status: string | null
          size: number
          user_id: string
          version: number | null
        }
        Insert: {
          content_hash?: string | null
          content_type: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          description?: string | null
          encryption_metadata?: Json | null
          file_path: string
          file_type: string
          filename: string
          folder_path?: string | null
          id?: string
          is_encrypted?: boolean | null
          metadata?: Json | null
          previous_versions?: Json | null
          scan_result?: Json | null
          scan_status?: string | null
          size: number
          user_id: string
          version?: number | null
        }
        Update: {
          content_hash?: string | null
          content_type?: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          description?: string | null
          encryption_metadata?: Json | null
          file_path?: string
          file_type?: string
          filename?: string
          folder_path?: string | null
          id?: string
          is_encrypted?: boolean | null
          metadata?: Json | null
          previous_versions?: Json | null
          scan_result?: Json | null
          scan_status?: string | null
          size?: number
          user_id?: string
          version?: number | null
        }
        Relationships: []
      }
      licensing_opportunities: {
        Row: {
          created_at: string
          id: string
          licensing_strategy: Json
          potential_matches: Json
          rights_status: Json
          royalty_tracking: Json
          song_metadata: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          licensing_strategy?: Json
          potential_matches?: Json
          rights_status?: Json
          royalty_tracking?: Json
          song_metadata?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          licensing_strategy?: Json
          potential_matches?: Json
          rights_status?: Json
          royalty_tracking?: Json
          song_metadata?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      matching_history: {
        Row: {
          artist_id: string
          created_at: string
          id: string
          match_score: number
          match_type: string
          matched_entity_id: string
          status: string | null
          updated_at: string
        }
        Insert: {
          artist_id: string
          created_at?: string
          id?: string
          match_score: number
          match_type: string
          matched_entity_id: string
          status?: string | null
          updated_at?: string
        }
        Update: {
          artist_id?: string
          created_at?: string
          id?: string
          match_score?: number
          match_type?: string
          matched_entity_id?: string
          status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      moderated_content: {
        Row: {
          content_id: string
          content_type: string
          created_at: string
          id: string
          moderated_at: string | null
          moderated_by: string | null
          reason: string | null
          status: string
        }
        Insert: {
          content_id: string
          content_type: string
          created_at?: string
          id?: string
          moderated_at?: string | null
          moderated_by?: string | null
          reason?: string | null
          status?: string
        }
        Update: {
          content_id?: string
          content_type?: string
          created_at?: string
          id?: string
          moderated_at?: string | null
          moderated_by?: string | null
          reason?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "moderated_content_moderated_by_fkey"
            columns: ["moderated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      music_production_analysis: {
        Row: {
          arrangement_feedback: Json | null
          created_at: string
          file_id: string
          id: string
          musician_recommendations: Json | null
          track_analysis: Json | null
          updated_at: string
          user_id: string
        }
        Insert: {
          arrangement_feedback?: Json | null
          created_at?: string
          file_id: string
          id?: string
          musician_recommendations?: Json | null
          track_analysis?: Json | null
          updated_at?: string
          user_id: string
        }
        Update: {
          arrangement_feedback?: Json | null
          created_at?: string
          file_id?: string
          id?: string
          musician_recommendations?: Json | null
          track_analysis?: Json | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "music_production_analysis_file_id_fkey"
            columns: ["file_id"]
            isOneToOne: false
            referencedRelation: "files"
            referencedColumns: ["id"]
          },
        ]
      }
      music_trends: {
        Row: {
          analysis_date: string
          created_at: string
          genre: string
          growth_rate: number
          id: string
          market_saturation: number
          popularity_score: number
          predicted_trajectory: Json | null
          regional_popularity: Json | null
          updated_at: string
        }
        Insert: {
          analysis_date?: string
          created_at?: string
          genre: string
          growth_rate: number
          id?: string
          market_saturation: number
          popularity_score: number
          predicted_trajectory?: Json | null
          regional_popularity?: Json | null
          updated_at?: string
        }
        Update: {
          analysis_date?: string
          created_at?: string
          genre?: string
          growth_rate?: number
          id?: string
          market_saturation?: number
          popularity_score?: number
          predicted_trajectory?: Json | null
          regional_popularity?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
      pitch_deck_downloads: {
        Row: {
          created_at: string
          download_count: number
          download_date: string
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          download_count?: number
          download_date?: string
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string
          download_count?: number
          download_date?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      profile_images: {
        Row: {
          created_at: string
          id: string
          image_url: string
          is_primary: boolean | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          image_url: string
          is_primary?: boolean | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          image_url?: string
          is_primary?: boolean | null
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          cover_photo_url: string | null
          created_at: string
          full_name: string | null
          id: string
          industry_sectors: string[] | null
          introduction: string | null
          languages: string[] | null
          portfolio_items: Json | null
          primary_role: string | null
          regions: string[] | null
          secondary_roles: string[] | null
          social_links: Json | null
          specializations: string[] | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          cover_photo_url?: string | null
          created_at?: string
          full_name?: string | null
          id: string
          industry_sectors?: string[] | null
          introduction?: string | null
          languages?: string[] | null
          portfolio_items?: Json | null
          primary_role?: string | null
          regions?: string[] | null
          secondary_roles?: string[] | null
          social_links?: Json | null
          specializations?: string[] | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          cover_photo_url?: string | null
          created_at?: string
          full_name?: string | null
          id?: string
          industry_sectors?: string[] | null
          introduction?: string | null
          languages?: string[] | null
          portfolio_items?: Json | null
          primary_role?: string | null
          regions?: string[] | null
          secondary_roles?: string[] | null
          social_links?: Json | null
          specializations?: string[] | null
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      record_labels: {
        Row: {
          created_at: string
          description: string | null
          genres: string[] | null
          id: string
          name: string
          roster_size: number | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          genres?: string[] | null
          id?: string
          name: string
          roster_size?: number | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          genres?: string[] | null
          id?: string
          name?: string
          roster_size?: number | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      storage_analytics: {
        Row: {
          created_at: string
          id: string
          last_download_at: string | null
          last_upload_at: string | null
          quota_limit: number
          storage_used: number
          total_downloads: number
          total_uploads: number
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          last_download_at?: string | null
          last_upload_at?: string | null
          quota_limit?: number
          storage_used?: number
          total_downloads?: number
          total_uploads?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          last_download_at?: string | null
          last_upload_at?: string | null
          quota_limit?: number
          storage_used?: number
          total_downloads?: number
          total_uploads?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          created_at: string
          current_period_end: string
          current_period_start: string
          id: string
          plan: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_period_end?: string
          current_period_start?: string
          id?: string
          plan?: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_period_end?: string
          current_period_start?: string
          id?: string
          plan?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_blocks: {
        Row: {
          blocked_id: string
          blocker_id: string
          created_at: string
          id: string
          reason: string | null
        }
        Insert: {
          blocked_id: string
          blocker_id: string
          created_at?: string
          id?: string
          reason?: string | null
        }
        Update: {
          blocked_id?: string
          blocker_id?: string
          created_at?: string
          id?: string
          reason?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_blocks_blocked_id_fkey"
            columns: ["blocked_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_blocks_blocker_id_fkey"
            columns: ["blocker_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_reports: {
        Row: {
          content_id: string
          content_type: string
          created_at: string
          id: string
          reason: string
          reported_id: string
          reporter_id: string
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          status: string
        }
        Insert: {
          content_id: string
          content_type: string
          created_at?: string
          id?: string
          reason: string
          reported_id: string
          reporter_id: string
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          status?: string
        }
        Update: {
          content_id?: string
          content_type?: string
          created_at?: string
          id?: string
          reason?: string
          reported_id?: string
          reporter_id?: string
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_reports_reported_id_fkey"
            columns: ["reported_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_reports_reporter_id_fkey"
            columns: ["reporter_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_reports_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      venue_bookings: {
        Row: {
          artist_id: string | null
          created_at: string
          event_date: string
          expected_attendance: number | null
          id: string
          revenue_split: Json | null
          status: string | null
          ticket_price: number | null
          updated_at: string
          venue_id: string | null
        }
        Insert: {
          artist_id?: string | null
          created_at?: string
          event_date: string
          expected_attendance?: number | null
          id?: string
          revenue_split?: Json | null
          status?: string | null
          ticket_price?: number | null
          updated_at?: string
          venue_id?: string | null
        }
        Update: {
          artist_id?: string | null
          created_at?: string
          event_date?: string
          expected_attendance?: number | null
          id?: string
          revenue_split?: Json | null
          status?: string | null
          ticket_price?: number | null
          updated_at?: string
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "venue_bookings_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venues"
            referencedColumns: ["id"]
          },
        ]
      }
      venues: {
        Row: {
          amenities: Json | null
          audience_demographics: Json | null
          booking_fee: number | null
          capacity: number
          created_at: string
          description: string | null
          genres: string[] | null
          historical_data: Json | null
          id: string
          location: string
          name: string
          technical_specs: Json | null
          updated_at: string
        }
        Insert: {
          amenities?: Json | null
          audience_demographics?: Json | null
          booking_fee?: number | null
          capacity: number
          created_at?: string
          description?: string | null
          genres?: string[] | null
          historical_data?: Json | null
          id?: string
          location: string
          name: string
          technical_specs?: Json | null
          updated_at?: string
        }
        Update: {
          amenities?: Json | null
          audience_demographics?: Json | null
          booking_fee?: number | null
          capacity?: number
          created_at?: string
          description?: string | null
          genres?: string[] | null
          historical_data?: Json | null
          id?: string
          location?: string
          name?: string
          technical_specs?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_rate_limit: {
        Args: {
          user_id: string
          action_type: string
          max_attempts: number
          window_minutes: number
        }
        Returns: boolean
      }
      is_user_blocked: {
        Args: {
          blocker_id: string
          blocked_id: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
