// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const supabaseUrl = "https://tmucketbprptnoukukyw.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRtdWNrZXRicHJwdG5vdWt1a3l3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzA3MDU1OTEsImV4cCI6MjA0NjI4MTU5MX0.9MZfA8ulcSc-99jFI5eAq7ZMK_2lF5TsPrnSLxZmYWI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(supabaseUrl, supabaseKey);