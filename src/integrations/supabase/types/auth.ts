export interface UserProfile {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  introduction: string | null;
  updated_at: string;
  created_at: string;
  cover_photo_url: string | null;
  social_links: Record<string, string>;
  portfolio_items: Array<{
    title: string;
    description: string;
    imageUrl: string;
    link?: string;
  }>;
}

export interface UserVerification {
  id: string;
  user_id: string;
  verification_type: string;
  verified_at: string | null;
  verification_data: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AuditLog {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id: string | null;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}