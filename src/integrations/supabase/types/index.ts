export * from './auth';
export * from './files';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      audit_logs: {
        Row: {
          id: string
          user_id: string
          action: string
          resource_type: string
          resource_id: string | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: Omit<AuditLog, 'id' | 'created_at'>
        Update: Partial<Omit<AuditLog, 'id'>>
      }
      user_verifications: {
        Row: UserVerification
        Insert: Omit<UserVerification, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<UserVerification, 'id'>>        
      }
      files: {
        Row: FileRow
        Insert: Omit<FileRow, 'id' | 'created_at'>
        Update: Partial<Omit<FileRow, 'id'>>
      }
      profile_images: {
        Row: ProfileImageRow
        Insert: Omit<ProfileImageRow, 'id' | 'created_at'>
        Update: Partial<Omit<ProfileImageRow, 'id'>>;
      }
      profiles: {
        Row: ProfileRow
        Insert: Omit<ProfileRow, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<ProfileRow, 'id'>>;
      }
    }
    Views: Record<string, never>
    Functions: {
      check_rate_limit: {
        Args: {
          user_id: string
          action_type: string
          max_attempts: number
          window_minutes: number
        }
        Returns: boolean
      }
      log_audit_event: {
        Args: {
          p_user_id: string
          p_action: string
          p_resource_type: string
          p_resource_id: string | null
          p_ip_address: string | null
          p_user_agent: string | null
        }
        Returns: string
      }
    }
    Enums: Record<string, never>
  }
}
