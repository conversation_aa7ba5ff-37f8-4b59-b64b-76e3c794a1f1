export interface FileRow {
  id: string;
  user_id: string;
  filename: string;
  file_path: string;
  file_type: string;
  size: number;
  content_type: string;
  created_at: string;
  metadata: Record<string, any> | null;
  folder_path: string | null;
  description: string | null;
  version: number;
  previous_versions: Array<{
    version: number;
    file_path: string;
    updated_at: string;
    size: number;
  }>;
  deleted_at: string | null;
  deleted_by: string | null;
  is_encrypted: boolean;
  encryption_metadata: Record<string, any> | null;
  content_hash: string | null;
  scan_status: string | null;
  scan_result: Record<string, any> | null;
}

export interface FileShare {
  id: string;
  file_id: string | null;
  user_id: string | null;
  permission_level: string | null;
  created_at: string;
  created_by: string | null;
}