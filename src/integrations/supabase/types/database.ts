export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface FileRow {
  id: string
  user_id: string
  filename: string
  file_path: string
  file_type: string
  size: number
  content_type: string
  created_at: string
  metadata: J<PERSON> | null
  folder_path: string | null
  description: string | null
  version: number
  previous_versions: Json[]
  deleted_at: string | null
  deleted_by: string | null
  is_encrypted: boolean
  encryption_metadata: Json | null
  content_hash: string | null
  scan_status: string | null
  scan_result: Json | null
}

export interface ProfileImageRow {
  id: string
  user_id: string
  image_url: string
  is_primary: boolean | null
  created_at: string
}

export interface ProfileRow {
  id: string
  username: string | null
  full_name: string | null
  avatar_url: string | null
  introduction: string | null
  updated_at: string
  created_at: string
  cover_photo_url: string | null
  social_links: Json | null
  portfolio_items: Json | null
}