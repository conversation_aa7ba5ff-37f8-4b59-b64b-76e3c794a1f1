import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Camera } from "lucide-react";
import { Profile } from "@/types/profile";

interface ProfileHeaderProps {
  profile: Profile;
  onImageUpload: (event: React.ChangeEvent<HTMLInputElement>, type: 'avatar' | 'cover') => Promise<void>;
  uploading: boolean;
}

export const ProfileHeader = ({ profile, onImageUpload, uploading }: ProfileHeaderProps) => {
  return (
    <>
      <div className="relative h-48 rounded-lg overflow-hidden bg-gray-100">
        {profile.cover_photo_url && (
          <img
            src={profile.cover_photo_url}
            alt="Cover"
            className="w-full h-full object-cover"
          />
        )}
        <label className="absolute bottom-4 right-4 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer">
          <Camera className="h-4 w-4" />
          <input
            type="file"
            className="hidden"
            accept="image/*"
            onChange={(e) => onImageUpload(e, 'cover')}
            disabled={uploading}
          />
        </label>
      </div>

      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <Avatar className="h-24 w-24">
            <AvatarImage src={profile.avatar_url || undefined} />
            <AvatarFallback>{profile.full_name?.charAt(0) || "U"}</AvatarFallback>
          </Avatar>
          <label className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer">
            <Camera className="h-4 w-4" />
            <input
              type="file"
              className="hidden"
              accept="image/*"
              onChange={(e) => onImageUpload(e, 'avatar')}
              disabled={uploading}
            />
          </label>
        </div>
      </div>
    </>
  );
};