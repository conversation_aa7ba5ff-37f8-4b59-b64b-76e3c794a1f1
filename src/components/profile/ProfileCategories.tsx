import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";

const ROLES = [
  "Artist",
  "Producer",
  "Manager",
  "Label Executive",
  "Venue Owner",
  "Event Organizer",
  "Music Publisher",
  "A&R Representative",
  "Booking Agent",
  "Music Journalist"
];

const SECTORS = [
  "Recording Industry",
  "Live Performance",
  "Music Publishing",
  "Digital Distribution",
  "Music Education",
  "Music Technology",
  "Artist Management",
  "Music Licensing",
  "Music Marketing",
  "Music Media"
];

const REGIONS = [
  "North America",
  "South America",
  "Europe",
  "Asia",
  "Africa",
  "Oceania",
  "Middle East",
  "Caribbean",
  "Central America",
  "Eastern Europe"
];

const LANGUAGES = [
  "English",
  "Spanish",
  "French",
  "German",
  "Mandarin",
  "Japanese",
  "Korean",
  "Portuguese",
  "Italian",
  "Arabic"
];

interface ProfileCategoriesProps {
  primaryRole: string | null;
  secondaryRoles: string[] | null;
  industrySectors: string[] | null;
  specializations: string[] | null;
  regions: string[] | null;
  languages: string[] | null;
  onChange: (field: string, value: any) => void;
}

export const ProfileCategories = ({
  primaryRole,
  secondaryRoles,
  industrySectors,
  specializations,
  regions,
  languages,
  onChange
}: ProfileCategoriesProps) => {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Primary Role</Label>
        <select
          className="w-full rounded-md border border-input bg-background px-3 py-2"
          value={primaryRole || ''}
          onChange={(e) => onChange('primary_role', e.target.value)}
        >
          <option value="">Select a role</option>
          {ROLES.map((role) => (
            <option key={role} value={role}>{role}</option>
          ))}
        </select>
      </div>

      <div className="space-y-2">
        <Label>Secondary Roles</Label>
        <MultiSelect
          options={ROLES}
          selected={secondaryRoles || []}
          onChange={(value) => onChange('secondary_roles', value)}
          placeholder="Select secondary roles"
        />
      </div>

      <div className="space-y-2">
        <Label>Industry Sectors</Label>
        <MultiSelect
          options={SECTORS}
          selected={industrySectors || []}
          onChange={(value) => onChange('industry_sectors', value)}
          placeholder="Select industry sectors"
        />
      </div>

      <div className="space-y-2">
        <Label>Specializations</Label>
        <Input
          placeholder="Add specializations (comma-separated)"
          value={specializations?.join(', ') || ''}
          onChange={(e) => onChange('specializations', e.target.value.split(',').map(s => s.trim()))}
        />
      </div>

      <div className="space-y-2">
        <Label>Regions</Label>
        <MultiSelect
          options={REGIONS}
          selected={regions || []}
          onChange={(value) => onChange('regions', value)}
          placeholder="Select regions"
        />
      </div>

      <div className="space-y-2">
        <Label>Languages</Label>
        <MultiSelect
          options={LANGUAGES}
          selected={languages || []}
          onChange={(value) => onChange('languages', value)}
          placeholder="Select languages"
        />
      </div>
    </div>
  );
};