import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useSession } from "@supabase/auth-helpers-react";

const MobileMenu = () => {
  const session = useSession();

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className="md:hidden"
          aria-label="Open mobile menu"
        >
          <Menu className="h-6 w-6" aria-hidden="true" />
        </Button>
      </SheetTrigger>
      <SheetContent 
        side="right" 
        className="w-[300px] sm:w-[400px]"
        role="dialog"
        aria-label="Mobile navigation menu"
      >
        <nav 
          className="flex flex-col gap-4"
          role="navigation"
          aria-label="Mobile navigation"
        >
          <Link 
            to="/" 
            className="text-lg font-semibold hover:text-primary"
            role="menuitem"
          >
            Home
          </Link>
          <Link 
            to="/pricing" 
            className="text-lg hover:text-primary"
            role="menuitem"
          >
            Pricing
          </Link>
          <Link 
            to="/about" 
            className="text-lg hover:text-primary"
            role="menuitem"
          >
            About Us
          </Link>
          <Link 
            to="/contact" 
            className="text-lg hover:text-primary"
            role="menuitem"
          >
            Contact
          </Link>
          <div 
            className="flex flex-col gap-2 mt-4"
            role="group"
            aria-label="Authentication"
          >
            {session ? (
              <Link to="/profile">
                <Button 
                  className="w-full" 
                  variant="outline"
                  aria-label="View profile"
                >
                  Profile
                </Button>
              </Link>
            ) : (
              <>
                <Link to="/auth">
                  <Button 
                    className="w-full" 
                    variant="outline"
                    aria-label="Log in to your account"
                  >
                    Log in
                  </Button>
                </Link>
                <Link to="/auth">
                  <Button 
                    className="w-full"
                    aria-label="Sign up for an account"
                  >
                    Sign up
                  </Button>
                </Link>
              </>
            )}
          </div>
        </nav>
      </SheetContent>
    </Sheet>
  );
};

export default MobileMenu;