import { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { useSecurityMiddleware } from "@/utils/securityMiddleware";
import { useToast } from "@/components/ui/use-toast";

interface ProtectedRouteProps {
  children: ReactNode;
  rateLimitConfig?: {
    maxAttempts: number;
    windowMinutes: number;
  };
}

export const ProtectedRoute = ({ children, rateLimitConfig }: ProtectedRouteProps) => {
  const { session } = useSecurityMiddleware([], rateLimitConfig);
  const { toast } = useToast();

  if (!session) {
    toast({
      title: "Authentication required",
      description: "Please log in to access this page",
      variant: "destructive",
    });
    return <Navigate to="/auth" replace />;
  }

  return <>{children}</>;
};