import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { FileRow } from "@/integrations/supabase/types/database";
import { getFilePreview } from "@/utils/filePreview";
import { FileListItem } from "./file/FileListItem";
import { FileListHeader } from "./file/FileListHeader";
import { FilePreview } from "./file/FilePreview";
import { VersionsDialog } from "./file/VersionsDialog";

export const FileList = () => {
  const session = useSession();
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState('/');
  const [showVersions, setShowVersions] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileRow | null>(null);
  const [includeDeleted, setIncludeDeleted] = useState(false);

  const { data: files, refetch } = useQuery({
    queryKey: ['files', session?.user.id, currentFolder, includeDeleted],
    queryFn: async () => {
      let query = supabase
        .from('files')
        .select('*')
        .eq('folder_path', currentFolder)
        .order('created_at', { ascending: false });

      if (!includeDeleted) {
        query = query.is('deleted_at', null);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as FileRow[];
    },
  });

  const handlePreview = async (file: FileRow) => {
    try {
      const url = await getFilePreview(file);
      if (url) {
        setPreviewUrl(url);
        setSelectedFile(file);
      } else {
        toast({
          title: "Preview not available",
          description: "This file type doesn't support preview.",
          variant: "default",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load preview",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (file: FileRow) => {
    try {
      const { error: dbError } = await supabase
        .from('files')
        .update({
          deleted_at: new Date().toISOString(),
          deleted_by: session?.user.id
        })
        .eq('id', file.id);

      if (dbError) throw dbError;

      refetch();
      toast({
        title: "Success",
        description: "File moved to trash",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleRestore = async (file: FileRow) => {
    try {
      const { error: dbError } = await supabase
        .from('files')
        .update({
          deleted_at: null,
          deleted_by: null
        })
        .eq('id', file.id);

      if (dbError) throw dbError;

      refetch();
      toast({
        title: "Success",
        description: "File restored successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <FileListHeader
        currentFolder={currentFolder}
        includeDeleted={includeDeleted}
        onFolderChange={setCurrentFolder}
        onToggleDeleted={() => setIncludeDeleted(!includeDeleted)}
      />
      
      <div className="grid gap-4">
        {files?.map((file) => (
          <FileListItem
            key={file.id}
            file={file}
            onPreview={handlePreview}
            onDelete={handleDelete}
            onRestore={handleRestore}
            onShowVersions={(file) => {
              setSelectedFile(file);
              setShowVersions(true);
            }}
          />
        ))}

        {files?.length === 0 && (
          <p className="text-center text-muted-foreground py-8">
            No files in this folder
          </p>
        )}
      </div>

      <FilePreview
        file={selectedFile}
        previewUrl={previewUrl}
        onClose={() => {
          setPreviewUrl(null);
          setSelectedFile(null);
        }}
      />

      <VersionsDialog
        file={selectedFile}
        open={showVersions}
        onOpenChange={setShowVersions}
      />
    </div>
  );
};