import { Star } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Music Producer",
      image: "https://images.unsplash.com/photo-1615729947596-a598e5de0ab3",
      quote: "QUINCY.AI has revolutionized how I collaborate with artists globally. The platform's AI tools have made international project management seamless."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Independent Artist",
      image: "https://images.unsplash.com/photo-1488972685288-c3fd157d7c7a",
      quote: "As an independent artist from Tokyo, this platform has opened doors to incredible global opportunities and collaborations I never thought possible."
    },
    {
      name: "<PERSON>",
      role: "Event Organizer",
      image: "https://images.unsplash.com/photo-1492321936769-b49830bc1d1e",
      quote: "The global directory has transformed how we book international artists. It's become our go-to platform for discovering diverse talent worldwide."
    }
  ];

  return (
    <div className="py-20 bg-gradient-to-br from-primary/5 to-secondary/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Trusted by Music Professionals Worldwide
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Hear from the people who are transforming the global music industry with QUINCY.AI
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-accent fill-current" />
                ))}
              </div>
              <p className="text-gray-600 mb-6 italic">"{testimonial.quote}"</p>
              <div className="flex items-center">
                <img 
                  src={testimonial.image} 
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <h4 className="font-semibold">{testimonial.name}</h4>
                  <p className="text-gray-600 text-sm">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Testimonials;