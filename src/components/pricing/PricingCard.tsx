import { Check, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { PricingPlan } from "@/data/pricingPlans";

interface PricingCardProps {
  plan: PricingPlan;
}

export const PricingCard = ({ plan }: PricingCardProps) => {
  return (
    <div 
      className={`rounded-xl border bg-card p-6 shadow-sm transition-all duration-300 hover:shadow-lg ${
        plan.popular ? 'border-primary ring-2 ring-primary ring-offset-2' : ''
      }`}
    >
      <div className="relative min-h-[600px] flex flex-col">
        {plan.popular && (
          <Badge className="absolute -top-3 right-0">Most Popular</Badge>
        )}
        
        <div>
          <h3 className="text-xl font-semibold">{plan.name}</h3>
          <p className="text-sm text-muted-foreground mt-1.5">{plan.description}</p>
        </div>
        
        <div className="mt-5 flex items-baseline">
          <span className="text-3xl font-bold">$</span>
          <span className="text-5xl font-bold">{plan.price}</span>
          <span className="ml-1 text-muted-foreground">/mo</span>
        </div>

        <div className="mt-6 space-y-3 text-sm text-muted-foreground">
          {plan.storage && (
            <div className="flex items-center">
              <Check className="mr-2 h-4 w-4 text-primary" />
              <span>{plan.storage} Storage</span>
            </div>
          )}
          {plan.messages && (
            <div className="flex items-center">
              <Check className="mr-2 h-4 w-4 text-primary" />
              <span>{plan.messages} Messages</span>
            </div>
          )}
          {plan.aiUsage && (
            <div className="flex items-center">
              <Check className="mr-2 h-4 w-4 text-primary" />
              <span>{plan.aiUsage} AI Usage</span>
            </div>
          )}
          {plan.teamSize && (
            <div className="flex items-center">
              <Check className="mr-2 h-4 w-4 text-primary" />
              <span>{plan.teamSize}</span>
            </div>
          )}
        </div>
        
        <ul className="mt-6 space-y-4 flex-grow">
          {plan.features.map((feature) => (
            <TooltipProvider key={feature.name}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <li className="flex items-center text-sm group cursor-help">
                    {feature.included ? (
                      <Check className="mr-2 h-4 w-4 text-primary flex-shrink-0" />
                    ) : (
                      <X className="mr-2 h-4 w-4 text-muted-foreground flex-shrink-0" />
                    )}
                    <span className={`${feature.included ? "" : "text-muted-foreground"} truncate`}>
                      {feature.name}
                    </span>
                  </li>
                </TooltipTrigger>
                {feature.description && (
                  <TooltipContent side="right" className="max-w-[200px]">
                    <p>{feature.description}</p>
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          ))}
        </ul>
        
        <Button 
          className="mt-6 w-full" 
          variant={plan.popular ? "default" : "outline"}
        >
          Get Started
        </Button>
      </div>
    </div>
  );
};