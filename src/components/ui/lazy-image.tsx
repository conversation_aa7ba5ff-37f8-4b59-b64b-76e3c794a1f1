import { useState, useEffect } from "react";
import { Skeleton } from "@/components/ui/skeleton";

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallback?: React.ReactNode;
  sizes?: string;
  srcSet?: string;
}

export const LazyImage = ({ 
  src, 
  alt, 
  className, 
  fallback, 
  sizes = "(max-width: 768px) 100vw, 50vw",
  srcSet,
  ...props 
}: LazyImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    if (!src) return;

    const img = new Image();
    img.src = src;
    
    if (srcSet) {
      img.srcset = srcSet;
    }

    img.onload = () => {
      setImageSrc(src);
      setIsLoading(false);
    };

    img.onerror = () => {
      setError(true);
      setIsLoading(false);
    };

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src, srcSet]);

  if (error) {
    return fallback || (
      <div className={`bg-muted flex items-center justify-center ${className}`}>
        <span className="text-muted-foreground">Failed to load image</span>
      </div>
    );
  }

  if (isLoading) {
    return fallback || <Skeleton className={`${className} bg-muted`} />;
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
      sizes={sizes}
      srcSet={srcSet}
      {...props}
    />
  );
};