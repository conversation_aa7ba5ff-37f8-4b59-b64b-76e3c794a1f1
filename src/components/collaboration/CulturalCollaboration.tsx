import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { MultiSelect } from "@/components/ui/multi-select";
import { Globe, MessageSquare, Music, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";
import { CollaborationPreferences } from "./CollaborationPreferences";
import { TranslationChat } from "./TranslationChat";
import { CollaborationSuggestions } from "./CollaborationSuggestions";

export function CulturalCollaboration() {
  const session = useSession();

  const { data: preferences } = useQuery({
    queryKey: ['cultural-preferences', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('cultural_collaborations')
        .select('*')
        .eq('user_id', session?.user.id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">
        Cross-Cultural Collaboration Assistant
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <CollaborationPreferences preferences={preferences} />
        <TranslationChat preferences={preferences} />
      </div>
      
      <div className="mt-8">
        <CollaborationSuggestions preferences={preferences} />
      </div>
    </div>
  );
}