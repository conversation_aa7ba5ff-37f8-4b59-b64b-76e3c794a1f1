import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, Music } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export function CollaborationSuggestions({ preferences }) {
  const { data: suggestions } = useQuery({
    queryKey: ['collaboration-suggestions', preferences?.musical_traditions],
    queryFn: async () => {
      if (!preferences?.musical_traditions?.length) return [];
      
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          cultural_collaborations (*)
        `)
        .contains('cultural_collaborations.musical_traditions', preferences.musical_traditions)
        .limit(6);

      if (error) throw error;
      return data;
    },
    enabled: !!preferences?.musical_traditions?.length,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Suggested Collaborations
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suggestions?.map((profile) => (
            <Card key={profile.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Music className="h-4 w-4" />
                  <h3 className="font-medium">{profile.full_name}</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  Traditions: {profile.cultural_collaborations?.musical_traditions?.join(', ')}
                </p>
                <Button variant="outline" className="w-full">
                  Connect
                </Button>
              </CardContent>
            </Card>
          ))}

          {!suggestions?.length && (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              <Users className="mx-auto h-12 w-12 mb-2" />
              <p>No collaboration suggestions yet. Try updating your preferences!</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}