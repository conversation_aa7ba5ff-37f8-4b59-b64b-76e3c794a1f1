import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import { MessageSquare } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export function TranslationChat({ preferences }) {
  const [message, setMessage] = useState("");
  const [translation, setTranslation] = useState("");
  const [targetLanguage, setTargetLanguage] = useState("");
  const [isTranslating, setIsTranslating] = useState(false);

  const handleTranslate = async () => {
    if (!message || !targetLanguage) return;
    
    setIsTranslating(true);
    try {
      const { data, error } = await supabase.functions.invoke('cultural-assistant', {
        body: {
          text: message,
          sourceLanguage: 'auto',
          targetLanguage,
          context: preferences?.musical_traditions?.join(', ')
        }
      });

      if (error) throw error;
      setTranslation(data);
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Real-time Translation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Textarea
            placeholder="Type your message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Select
            value={targetLanguage}
            onValueChange={setTargetLanguage}
          >
            <option value="">Select language</option>
            {preferences?.preferred_languages?.map((lang) => (
              <option key={lang} value={lang}>{lang}</option>
            ))}
          </Select>
          
          <Button 
            onClick={handleTranslate}
            disabled={isTranslating || !message || !targetLanguage}
          >
            Translate
          </Button>
        </div>

        {translation && (
          <div className="mt-4 p-4 bg-secondary rounded-lg">
            <p className="text-sm font-medium">Translation:</p>
            <p className="mt-2">{translation}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}