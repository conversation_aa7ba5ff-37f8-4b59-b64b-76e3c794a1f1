import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";
import { Button } from "@/components/ui/button";
import { Globe, Music } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/use-toast";

const LANGUAGES = [
  "English", "Spanish", "Mandarin", "Hindi", "Arabic", 
  "French", "Portuguese", "Japanese", "Korean", "German"
];

const MUSICAL_TRADITIONS = [
  "Western Classical", "Jazz", "Blues", "Latin", "African",
  "Indian Classical", "East Asian", "Middle Eastern", "Caribbean", "Electronic"
];

export function CollaborationPreferences({ preferences }) {
  const queryClient = useQueryClient();

  const updatePreferences = useMutation({
    mutationFn: async (values: any) => {
      const { data, error } = await supabase
        .from('cultural_collaborations')
        .upsert(values)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cultural-preferences'] });
      toast({
        title: "Preferences updated",
        description: "Your collaboration preferences have been saved.",
      });
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          Cultural Preferences
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Preferred Languages</Label>
          <MultiSelect
            options={LANGUAGES}
            selected={preferences?.preferred_languages || []}
            onChange={(value) => 
              updatePreferences.mutate({
                ...preferences,
                preferred_languages: value
              })
            }
            placeholder="Select languages"
          />
        </div>

        <div className="space-y-2">
          <Label>Musical Traditions</Label>
          <MultiSelect
            options={MUSICAL_TRADITIONS}
            selected={preferences?.musical_traditions || []}
            onChange={(value) =>
              updatePreferences.mutate({
                ...preferences,
                musical_traditions: value
              })
            }
            placeholder="Select traditions"
          />
        </div>
      </CardContent>
    </Card>
  );
}