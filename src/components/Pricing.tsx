import { pricingPlans } from "@/data/pricingPlans";
import { PricingCard } from "./pricing/PricingCard";

const Pricing = () => {
  return (
    <section 
      className="py-10 px-4 md:py-16 bg-gray-50/50"
      aria-labelledby="pricing-title"
    > 
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-8">
          <h2 
            id="pricing-title"
            className="text-3xl font-bold sm:text-4xl mb-3"
          >
            Simple, Transparent Pricing
          </h2>
          <p className="text-base text-muted-foreground">
            Choose the perfect plan for your needs. Scale your music business with our comprehensive features and AI-powered tools.
          </p>
        </div>
        
        <div 
          className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 max-w-[1600px] mx-auto"
          role="list"
          aria-label="Pricing plans"
        >
          {pricingPlans.map((plan) => (
            <div key={plan.name} role="listitem">
              <PricingCard plan={plan} />
            </div>
          ))}
        </div>

        <div 
          className="mt-8 text-center text-sm text-muted-foreground"
          aria-label="Additional pricing information"
        >
          <p>All plans include basic features like profile creation and community access.</p>
          <p className="mt-1">Need a custom solution? Contact our sales team for enterprise options.</p>
        </div>
      </div>
    </section>
  );
};

export default Pricing;