import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface TrendChartProps {
  data: any[];
  isLoading: boolean;
}

export const TrendChart = ({ data, isLoading }: TrendChartProps) => {
  if (isLoading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-[300px] w-full" />
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Trend Analysis</h2>
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="genre" />
            <YAxis />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="popularity_score"
              stroke="#FF6B00"
              name="Popularity"
            />
            <Line
              type="monotone"
              dataKey="growth_rate"
              stroke="#8E24AA"
              name="Growth"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};