import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp, TrendingDown, BarChart2 } from "lucide-react";

interface TrendInsightsProps {
  data: any[];
  isLoading: boolean;
}

export const TrendInsights = ({ data, isLoading }: TrendInsightsProps) => {
  if (isLoading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-[300px] w-full" />
      </Card>
    );
  }

  const latestTrend = data[0];

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Market Insights</h2>
      {latestTrend ? (
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <BarChart2 className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Popularity Score</p>
              <p className="text-2xl font-semibold">
                {latestTrend.popularity_score.toFixed(1)}%
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {latestTrend.growth_rate >= 0 ? (
              <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            ) : (
              <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-lg">
                <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
            )}
            <div>
              <p className="text-sm text-muted-foreground">Growth Rate</p>
              <p className="text-2xl font-semibold">
                {latestTrend.growth_rate.toFixed(1)}%
              </p>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Market Saturation</h3>
            <div className="w-full bg-muted rounded-full h-2.5">
              <div
                className="bg-primary h-2.5 rounded-full"
                style={{ width: `${latestTrend.market_saturation}%` }}
              />
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {latestTrend.market_saturation.toFixed(1)}% market saturation
            </p>
          </div>
        </div>
      ) : (
        <p className="text-muted-foreground">No trend data available</p>
      )}
    </Card>
  );
};