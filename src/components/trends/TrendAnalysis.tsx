import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface TrendAnalysisProps {
  data: any[];
  isLoading: boolean;
}

export const TrendAnalysis = ({ data, isLoading }: TrendAnalysisProps) => {
  if (isLoading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-[200px] w-full" />
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Detailed Analysis</h2>
      <Accordion type="single" collapsible className="w-full">
        {data.map((trend) => (
          <AccordionItem key={trend.id} value={trend.id}>
            <AccordionTrigger>
              {trend.genre} - {new Date(trend.analysis_date).toLocaleDateString()}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4 pt-2">
                <div>
                  <h4 className="font-medium mb-1">AI Analysis</h4>
                  <p className="text-sm text-muted-foreground whitespace-pre-line">
                    {trend.predicted_trajectory.analysis}
                  </p>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </Card>
  );
};