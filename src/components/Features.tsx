import { 
  Book<PERSON>pen, 
  MessageSquare, 
  PenTool, 
  Calendar, 
  Users, 
  BarChart 
} from "lucide-react";

const Features = () => {
  const features = [
    {
      icon: <BookOpen className="w-6 h-6" aria-hidden="true" />,
      title: "Global Directory",
      description: "Access profiles of artists, professionals, venues, and organizations from every corner of the world"
    },
    {
      icon: <PenTool className="w-6 h-6" aria-hidden="true" />,
      title: "AI Grant Writing",
      description: "Create compelling grant proposals with our AI-powered writing assistant"
    },
    {
      icon: <MessageSquare className="w-6 h-6" aria-hidden="true" />,
      title: "Global Networking",
      description: "Connect directly with industry professionals across continents"
    },
    {
      icon: <Calendar className="w-6 h-6" aria-hidden="true" />,
      title: "Event Management",
      description: "Discover and list music events, festivals, and workshops worldwide"
    },
    {
      icon: <Users className="w-6 h-6" aria-hidden="true" />,
      title: "Community Forums",
      description: "Join global discussions and share experiences with industry peers"
    },
    {
      icon: <BarChart className="w-6 h-6" aria-hidden="true" />,
      title: "Analytics Dashboard",
      description: "Track your global reach and networking activities"
    }
  ];

  return (
    <section 
      className="py-8 bg-white"
      aria-labelledby="features-title"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 
            id="features-title"
            className="text-3xl md:text-4xl font-bold mb-3"
          >
            Powerful Features for the Global Music Industry
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Everything you need to succeed in the international music industry, all in one place
          </p>
        </div>
        <div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          role="list"
          aria-label="Feature list"
        >
          {features.map((feature, index) => (
            <div 
              key={index}
              className="p-4 rounded-lg border border-gray-200 hover:border-primary/20 transition-all duration-300 hover:shadow-lg"
              role="listitem"
              tabIndex={0}
            >
              <div 
                className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-3 text-primary"
                aria-hidden="true"
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold mb-1">{feature.title}</h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;