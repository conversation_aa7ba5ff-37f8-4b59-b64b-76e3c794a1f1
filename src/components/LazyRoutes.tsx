import { lazy } from "react";
import { Routes, Route } from "react-router-dom";
import { RouteErrorBoundary } from "@/components/RouteErrorBoundary";
import { protectedRoutes, rateLimitedRoutes } from "@/utils/protectedRoutes";
import { ProtectedRoute } from "./ProtectedRoute";

const retryLoadComponent = (fn: () => Promise<any>, retries = 3) =>
  new Promise((resolve, reject) => {
    fn()
      .then(resolve)
      .catch((error) => {
        if (retries === 0) {
          reject(error);
          return;
        }
        setTimeout(() => {
          retryLoadComponent(fn, retries - 1).then(resolve, reject);
        }, 1000);
      });
  });

// Lazy load components
const Index = lazy(() => retryLoadComponent(() => import("../pages/Index")));
const AuthPage = lazy(() => retryLoadComponent(() => import("../pages/Auth")));
const AIFeatures = lazy(() => retryLoadComponent(() => import("../pages/AIFeatures")));
const AboutUs = lazy(() => retryLoadComponent(() => import("../pages/AboutUs")));
const Contact = lazy(() => retryLoadComponent(() => import("../pages/Contact")));
const Blog = lazy(() => retryLoadComponent(() => import("../pages/Blog")));
const HelpCenter = lazy(() => retryLoadComponent(() => import("../pages/HelpCenter")));
const Terms = lazy(() => retryLoadComponent(() => import("../pages/Terms")));
const Privacy = lazy(() => retryLoadComponent(() => import("../pages/Privacy")));

export const LazyRoutes = () => {
  return (
    <Routes>
      <Route errorElement={<RouteErrorBoundary />}>
        <Route path="/" element={<Index />} />
        <Route path="/auth" element={<AuthPage />} />
        <Route path="/ai-features" element={<AIFeatures />} />
        <Route path="/about" element={<AboutUs />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/blog" element={<Blog />} />
        <Route path="/help" element={<HelpCenter />} />
        <Route path="/terms" element={<Terms />} />
        <Route path="/privacy" element={<Privacy />} />
        
        {/* Protected routes */}
        {protectedRoutes.map(route => {
          const Component = lazy(() => retryLoadComponent(() => import(`../pages${route}`)));
          return (
            <Route
              key={route}
              path={route}
              element={
                <ProtectedRoute
                  rateLimitConfig={rateLimitedRoutes[route]}
                >
                  <Component />
                </ProtectedRoute>
              }
            />
          );
        })}
      </Route>
    </Routes>
  );
};