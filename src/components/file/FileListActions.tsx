import { But<PERSON> } from "@/components/ui/button";
import { History, Rotate<PERSON>cw, Trash2, Eye } from "lucide-react";
import { FileRow } from "@/integrations/supabase/types/database";

interface FileListActionsProps {
  file: FileRow;
  onPreview: (file: FileRow) => void;
  onDelete: (file: FileRow) => void;
  onRestore: (file: FileRow) => void;
  onShowVersions: (file: FileRow) => void;
}

export const FileListActions = ({
  file,
  onPreview,
  onDelete,
  onRestore,
  onShowVersions,
}: FileListActionsProps) => {
  const canPreview = ['image/', 'application/pdf', 'text/', 'audio/'].some(type => 
    file.content_type.startsWith(type)
  );

  return (
    <div 
      className="flex space-x-2"
      role="group"
      aria-label="File actions"
    >
      {canPreview && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPreview(file)}
          aria-label={`Preview ${file.filename}`}
        >
          <Eye className="w-4 h-4 mr-1" aria-hidden="true" />
          Preview
        </Button>
      )}
      {file.version > 1 && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onShowVersions(file)}
          aria-label={`View version history for ${file.filename}`}
        >
          <History className="w-4 h-4" aria-hidden="true" />
          <span className="sr-only">Version History</span>
        </Button>
      )}
      {file.deleted_at ? (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onRestore(file)}
          aria-label={`Restore ${file.filename} from trash`}
        >
          <RotateCcw className="w-4 h-4" aria-hidden="true" />
          <span className="sr-only">Restore</span>
        </Button>
      ) : (
        <Button
          variant="destructive"
          size="sm"
          onClick={() => onDelete(file)}
          aria-label={`Move ${file.filename} to trash`}
        >
          <Trash2 className="w-4 h-4" aria-hidden="true" />
          <span className="sr-only">Delete</span>
        </Button>
      )}
    </div>
  );
};