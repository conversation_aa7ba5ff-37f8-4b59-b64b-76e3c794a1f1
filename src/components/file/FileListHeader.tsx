import { FolderTree } from "lucide-react";
import { Button } from "@/components/ui/button";
import { FileBreadcrumb } from "./FileBreadcrumb";
import { FileSearch } from "./FileSearch";

interface FileListHeaderProps {
  currentFolder: string;
  searchQuery: string;
  includeDeleted: boolean;
  onFolderChange: (path: string) => void;
  onSearchChange: (query: string) => void;
  onToggleDeleted: () => void;
}

export const FileListHeader = ({
  currentFolder,
  searchQuery,
  includeDeleted,
  onFolderChange,
  onSearchChange,
  onToggleDeleted,
}: FileListHeaderProps) => {
  return (
    <div className="space-y-4">
      <FileBreadcrumb
        currentPath={currentFolder}
        onNavigate={onFolderChange}
      />
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <FileSearch
            value={searchQuery}
            onChange={onSearchChange}
          />
        </div>
        <Button
          variant="outline"
          onClick={onToggleDeleted}
        >
          {includeDeleted ? "Hide Deleted" : "Show Deleted"}
        </Button>
      </div>
    </div>
  );
};