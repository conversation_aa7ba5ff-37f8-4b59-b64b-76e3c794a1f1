import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileRow } from "@/integrations/supabase/types/database";
import { FileText, Music, X } from "lucide-react";
import { useEffect } from "react";

interface FilePreviewProps {
  file: FileRow | null;
  previewUrl: string | null;
  onClose: () => void;
}

export const FilePreview = ({ file, previewUrl, onClose }: FilePreviewProps) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (previewUrl) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [previewUrl, onClose]);

  if (!previewUrl || !file) return null;

  const renderPreview = () => {
    if (file.content_type.startsWith('image/')) {
      return (
        <img
          src={previewUrl}
          alt={file.filename}
          className="max-w-full h-auto rounded-lg"
        />
      );
    }

    if (file.content_type.startsWith('audio/')) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-center">
            <Music className="w-16 h-16 text-muted-foreground" aria-hidden="true" />
          </div>
          <audio 
            controls 
            className="w-full"
            aria-label={`Audio player for ${file.filename}`}
          >
            <source src={previewUrl} type={file.content_type} />
            Your browser does not support the audio element.
          </audio>
        </div>
      );
    }

    if (file.content_type === 'application/pdf') {
      return (
        <iframe
          src={previewUrl}
          className="w-full h-[600px] rounded-lg"
          title={`PDF preview of ${file.filename}`}
        />
      );
    }

    if (file.content_type.startsWith('text/')) {
      return (
        <div className="flex items-center justify-center p-8">
          <FileText className="w-16 h-16 text-muted-foreground" aria-hidden="true" />
          <a
            href={previewUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-4 text-primary hover:underline"
            aria-label={`Open ${file.filename} in new tab`}
          >
            Open Text File
          </a>
        </div>
      );
    }

    return (
      <div 
        className="text-center text-muted-foreground"
        role="alert"
      >
        Preview not available for this file type
      </div>
    );
  };

  return (
    <Dialog open={!!previewUrl} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-4xl">
        <DialogTitle className="sr-only">
          Preview of {file.filename}
        </DialogTitle>
        <div className="relative">
          {renderPreview()}
          <Button
            className="absolute top-2 right-2"
            variant="destructive"
            size="sm"
            onClick={onClose}
            aria-label="Close preview"
          >
            <X className="w-4 h-4" aria-hidden="true" />
            <span className="sr-only">Close</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};