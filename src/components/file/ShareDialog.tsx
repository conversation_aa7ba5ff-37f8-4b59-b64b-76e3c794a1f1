import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { FileRow } from "@/integrations/supabase/types/database";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";

interface ShareDialogProps {
  file: Partial<FileRow> & { id: string; user_id: string };
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ShareDialog = ({ file, open, onOpenChange }: ShareDialogProps) => {
  const session = useSession();
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [permission, setPermission] = useState("read");

  const handleShare = async () => {
    try {
      // First, get the user ID for the email
      const { data: users, error: userError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (userError || !users) {
        throw new Error("User not found");
      }

      const { error: shareError } = await supabase
        .from('file_shares')
        .insert({
          file_id: file.id,
          user_id: users.id,
          permission_level: permission,
          created_by: session?.user.id
        });

      if (shareError) throw shareError;

      toast({
        title: "Success",
        description: "File shared successfully",
      });
      
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Share {file.filename}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Input
              placeholder="Enter email address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div>
            <Select value={permission} onValueChange={setPermission}>
              <SelectTrigger>
                <SelectValue placeholder="Select permission" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="read">Read</SelectItem>
                <SelectItem value="write">Write</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button onClick={handleShare}>Share</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};