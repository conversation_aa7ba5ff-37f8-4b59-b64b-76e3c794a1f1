import { Progress } from "@/components/ui/progress";

interface FileUploadProgressProps {
  uploading: boolean;
  progress: number;
}

export const FileUploadProgress = ({ uploading, progress }: FileUploadProgressProps) => {
  if (!uploading) return null;

  return (
    <div className="space-y-2">
      <Progress value={progress} />
      <p className="text-sm text-center">{Math.round(progress)}%</p>
    </div>
  );
};