import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { getBucketForFileType } from "@/utils/fileUtils";
import { validateFile } from "@/utils/fileValidation";
import { useStorageQuota } from "@/hooks/useStorageQuota";
import { FileUploadPreview } from "./FileUploadPreview";
import { FileUploadProgress } from "./FileUploadProgress";

export const FileUpload = () => {
  const session = useSession();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [preview, setPreview] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState('/');
  const [description, setDescription] = useState('');
  
  const { data: quota } = useStorageQuota();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0];
      if (!file) return;

      const availableSpace = (quota?.quota_limit || 0) - (quota?.storage_used || 0);
      validateFile(file, availableSpace);

      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setPreview(null);
      }

      await handleUpload(file);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleUpload = async (file: File) => {
    try {
      setUploading(true);
      setProgress(0);

      const bucket = getBucketForFileType(file.type);
      const fileExt = file.name.split('.').pop();
      const normalizedFolderPath = folderPath.startsWith('/') ? folderPath : `/${folderPath}`;
      const filePath = `${normalizedFolderPath}/${Math.random()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      const metadata = {
        originalName: file.name,
        lastModified: file.lastModified,
        type: file.type,
      };

      const { error: dbError } = await supabase
        .from('files')
        .insert({
          user_id: session?.user.id,
          filename: file.name,
          file_path: filePath,
          file_type: file.type.split('/')[0],
          size: file.size,
          content_type: file.type,
          folder_path: normalizedFolderPath,
          description: description,
          metadata
        });

      if (dbError) throw dbError;

      toast({
        title: "Success",
        description: "File uploaded successfully",
      });
      
      setDescription('');
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <Input
          placeholder="Folder path (e.g., /documents/work)"
          value={folderPath}
          onChange={(e) => setFolderPath(e.target.value)}
        />
        <Input
          placeholder="File description (optional)"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </div>

      <div className="flex flex-col items-center p-8 border-2 border-dashed rounded-lg hover:border-primary transition-colors">
        <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
        <Button variant="outline" disabled={uploading}>
          <label className="cursor-pointer">
            {uploading ? "Uploading..." : "Select File"}
            <input
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept={Object.values(ALLOWED_FILE_TYPES).flat().join(',')}
              disabled={uploading}
            />
          </label>
        </Button>
        <p className="mt-2 text-sm text-muted-foreground">
          Available space: {formatBytes((quota?.quota_limit || 0) - (quota?.storage_used || 0))}
        </p>
      </div>

      <FileUploadProgress uploading={uploading} progress={progress} />
      <FileUploadPreview preview={preview} />
    </div>
  );
};

const formatBytes = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};