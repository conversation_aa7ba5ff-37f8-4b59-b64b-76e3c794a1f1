import { File, Image, Music } from "lucide-react";
import { FileRow } from "@/integrations/supabase/types/database";
import { formatFileSize } from "@/utils/fileUtils";
import { FileListActions } from "./FileListActions";

interface FileListItemProps {
  file: FileRow;
  onPreview: (file: FileRow) => void;
  onDelete: (file: FileRow) => void;
  onRestore: (file: FileRow) => void;
  onShowVersions: (file: FileRow) => void;
  isFocused?: boolean;
}

export const FileListItem = ({
  file,
  onPreview,
  onDelete,
  onRestore,
  onShowVersions,
  isFocused,
}: FileListItemProps) => {
  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) return <Image className="w-5 h-5" aria-hidden="true" />;
    if (contentType.startsWith('audio/')) return <Music className="w-5 h-5" aria-hidden="true" />;
    return <File className="w-5 h-5" aria-hidden="true" />;
  };

  return (
    <div 
      className={`flex items-center justify-between p-4 border rounded-lg transition-colors ${
        isFocused ? 'ring-2 ring-primary' : 'hover:bg-accent/50'
      }`}
      role="article"
      aria-label={`${file.filename} - ${formatFileSize(file.size)}`}
    >
      <div className="flex items-center space-x-4">
        {getFileIcon(file.content_type)}
        <div>
          <p className="font-medium">{file.filename}</p>
          <p className="text-sm text-muted-foreground">
            {formatFileSize(file.size)} • {new Date(file.created_at).toLocaleDateString()}
            {file.version > 1 && ` • Version ${file.version}`}
          </p>
          {file.description && (
            <p className="text-sm text-muted-foreground mt-1" aria-label="File description">
              {file.description}
            </p>
          )}
          {file.deleted_at && (
            <p 
              className="text-sm text-red-500 mt-1" 
              role="status" 
              aria-label="File deletion status"
            >
              Deleted on {new Date(file.deleted_at).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
      
      <FileListActions
        file={file}
        onPreview={onPreview}
        onDelete={onDelete}
        onRestore={onRestore}
        onShowVersions={onShowVersions}
      />
    </div>
  );
};