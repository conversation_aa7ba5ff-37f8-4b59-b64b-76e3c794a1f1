import { FileRow } from "@/integrations/supabase/types/database";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { formatFileSize } from "@/utils/fileUtils";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface VersionsDialogProps {
  file: FileRow | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const VersionsDialog = ({ file, open, onOpenChange }: VersionsDialogProps) => {
  const { toast } = useToast();

  const handleRestoreVersion = async (version: any) => {
    try {
      const { error: dbError } = await supabase
        .from('files')
        .update({
          file_path: version.file_path
        })
        .eq('id', file?.id);

      if (dbError) throw dbError;

      onOpenChange(false);
      toast({
        title: "Success",
        description: "File version restored successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  if (!file) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Previous Versions</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {file.previous_versions.map((version: any, index: number) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div>
                <p>Version {version.version}</p>
                <p className="text-sm text-muted-foreground">
                  {formatFileSize(version.size)} • {new Date(version.updated_at).toLocaleDateString()}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRestoreVersion(version)}
              >
                Restore
              </Button>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};