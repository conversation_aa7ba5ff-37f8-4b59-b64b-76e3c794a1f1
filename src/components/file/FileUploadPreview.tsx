import { Image } from "lucide-react";

interface FileUploadPreviewProps {
  preview: string | null;
}

export const FileUploadPreview = ({ preview }: FileUploadPreviewProps) => {
  if (!preview) return null;

  return (
    <div className="mt-4">
      <p className="text-sm font-medium mb-2">Preview:</p>
      <img
        src={preview}
        alt="Preview"
        className="max-w-full h-auto rounded-lg max-h-[200px] object-contain"
      />
    </div>
  );
};