import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { FileRow } from "@/integrations/supabase/types/database";
import { FileListItem } from "./FileListItem";
import { FileListHeader } from "./FileListHeader";
import { FileListSkeleton } from "./FileListSkeleton";
import { VersionsDialog } from "./VersionsDialog";
import { useFileOperations } from "@/hooks/useFileOperations";
import { getBucketForFileType } from "@/utils/fileUtils";
import { useToast } from "@/components/ui/use-toast";

export const FileList = () => {
  const session = useSession();
  const queryClient = useQueryClient();
  const { deleteFile, restoreFile } = useFileOperations();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentFolder, setCurrentFolder] = useState('/');
  const [searchQuery, setSearchQuery] = useState('');
  const [showVersions, setShowVersions] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileRow | null>(null);
  const [includeDeleted, setIncludeDeleted] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const { toast } = useToast();

  const { data: files, isLoading } = useQuery({
    queryKey: ['files', session?.user.id, currentFolder, includeDeleted, searchQuery],
    queryFn: async () => {
      let query = supabase
        .from('files')
        .select('*')
        .eq('folder_path', currentFolder)
        .order('created_at', { ascending: false });

      if (!includeDeleted) {
        query = query.is('deleted_at', null);
      }

      if (searchQuery) {
        query = query.ilike('filename', `%${searchQuery}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as FileRow[];
    },
    staleTime: 1000 * 60 * 5,
  });

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(Math.min((files?.length || 0) - 1, index + 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(Math.max(0, index - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (files?.[index]) {
          handlePreview(files[index]);
        }
        break;
      case 'Delete':
        e.preventDefault();
        if (files?.[index] && !files[index].deleted_at) {
          deleteFile.mutate(files[index]);
          toast({
            title: "File deleted",
            description: "The file has been moved to trash",
          });
        }
        break;
    }
  };

  const handlePreview = async (file: FileRow) => {
    if (file.content_type.startsWith('image/')) {
      const bucket = getBucketForFileType(file.content_type);
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(file.file_path);
      setPreviewUrl(publicUrl);
      setSelectedFile(file);
    }
  };

  if (isLoading) {
    return <FileListSkeleton />;
  }

  return (
    <div 
      className="space-y-4"
      role="region"
      aria-label="File list"
    >
      <FileListHeader
        currentFolder={currentFolder}
        searchQuery={searchQuery}
        includeDeleted={includeDeleted}
        onFolderChange={setCurrentFolder}
        onSearchChange={setSearchQuery}
        onToggleDeleted={() => setIncludeDeleted(!includeDeleted)}
      />
      
      <div 
        className="grid gap-4"
        role="list"
        aria-label="Files"
      >
        {files?.map((file, index) => (
          <div
            key={file.id}
            onKeyDown={(e) => handleKeyDown(e, index)}
            tabIndex={focusedIndex === index ? 0 : -1}
            role="listitem"
          >
            <FileListItem
              file={file}
              onPreview={handlePreview}
              onDelete={(file) => deleteFile.mutate(file)}
              onRestore={(file) => restoreFile.mutate(file)}
              onShowVersions={(file) => {
                setSelectedFile(file);
                setShowVersions(true);
              }}
              isFocused={focusedIndex === index}
            />
          </div>
        ))}

        {files?.length === 0 && (
          <p 
            className="text-center text-muted-foreground py-8"
            role="status"
          >
            No files found
          </p>
        )}
      </div>

      {previewUrl && (
        <div 
          className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4"
          role="dialog"
          aria-label="File preview"
        >
          <div className="bg-card p-4 rounded-lg max-w-2xl w-full">
            <div className="relative">
              <img
                src={previewUrl}
                alt={selectedFile?.filename || "Preview"}
                className="max-w-full h-auto rounded-lg"
              />
              <Button
                className="absolute top-2 right-2"
                variant="destructive"
                size="sm"
                onClick={() => {
                  setPreviewUrl(null);
                  setSelectedFile(null);
                }}
                aria-label="Close preview"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      <VersionsDialog
        file={selectedFile}
        open={showVersions}
        onOpenChange={setShowVersions}
      />
    </div>
  );
};