import { Link } from "react-router-dom";
import { useTheme } from "next-themes";
import { Moon, Sun, Sparkles, BarChart2, Globe, Music, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useSession } from "@supabase/auth-helpers-react";
import { useSecurityMiddleware } from "@/utils/securityMiddleware";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useEffect } from "react";
import MobileMenu from "./MobileMenu";
import { logAuditEvent } from "@/utils/auditLog";

const Header = () => {
  const { theme, setTheme } = useTheme();
  const session = useSession();
  const { session: secureSession } = useSecurityMiddleware();

  useEffect(() => {
    // Set security headers
    const setSecurityHeaders = () => {
      if (typeof window !== 'undefined') {
        // These would normally be set server-side, but we're adding them here for demonstration
        document.querySelector('meta[http-equiv="Content-Security-Policy"]')?.remove();
        const cspMeta = document.createElement('meta');
        cspMeta.httpEquiv = 'Content-Security-Policy';
        cspMeta.content = "default-src 'self'; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';";
        document.head.appendChild(cspMeta);
      }
    };

    setSecurityHeaders();
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Close any open menus
        document.querySelector('[data-state="open"]')?.setAttribute('data-state', 'closed');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleSignOut = async () => {
    if (session?.user) {
      await logAuditEvent(session.user.id, 'sign_out', 'auth');
    }
    await supabase.auth.signOut();
  };

  return (
    <header 
      className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b" 
      role="banner"
      aria-label="Main navigation"
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-14">
          <Link 
            to="/" 
            className="text-xl font-bold"
            aria-label="Quincy.AI Home"
          >
            Quincy.AI
          </Link>

          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList aria-label="Main Menu">
              <NavigationMenuItem>
                <NavigationMenuTrigger 
                  aria-label="Features menu"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      e.currentTarget.click();
                    }
                  }}
                >
                  Features
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <nav 
                    className="grid gap-3 p-4 w-[400px]" 
                    aria-label="Features navigation"
                    role="menu"
                  >
                    <Link 
                      to="/matching" 
                      className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                      role="menuitem"
                      tabIndex={0}
                      aria-label="AI Talent Matching - Find your perfect match in the music industry using AI"
                    >
                      <div className="flex items-center gap-2">
                        <Sparkles className="h-4 w-4" aria-hidden="true" />
                        <div className="text-sm font-medium leading-none">AI Talent Matching</div>
                      </div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Find your perfect match in the music industry using AI
                      </p>
                    </Link>
                    <Link to="/trends" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                      <div className="flex items-center gap-2">
                        <BarChart2 className="h-4 w-4" />
                        <div className="text-sm font-medium leading-none">Trend Prediction</div>
                      </div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Analyze global music trends and predict emerging genres
                      </p>
                    </Link>
                    <Link to="/production" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                      <div className="flex items-center gap-2">
                        <Music className="h-4 w-4" />
                        <div className="text-sm font-medium leading-none">AI Production Advisor</div>
                      </div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Get AI-powered mixing, mastering, and arrangement feedback
                      </p>
                    </Link>
                    <Link to="/collaboration" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <div className="text-sm font-medium leading-none">Cultural Collaboration</div>
                      </div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Connect with artists globally with AI-powered translation
                      </p>
                    </Link>
                    <Link to="/ai-features" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground bg-muted">
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4" />
                        <div className="text-sm font-medium leading-none">View All AI Features</div>
                      </div>
                      <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                        Explore our complete suite of AI-powered tools
                      </p>
                    </Link>
                  </nav>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/pricing" className="text-sm hover:text-primary transition-colors px-4 py-2">
                  Pricing
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/about" className="text-sm hover:text-primary transition-colors px-4 py-2">
                  About Us
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/contact" className="text-sm hover:text-primary transition-colors px-4 py-2">
                  Contact
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          <div className="flex items-center space-x-4" role="group" aria-label="User actions">
            <div className="flex items-center space-x-2">
              <Sun className="h-4 w-4" aria-hidden="true" />
              <Switch
                checked={theme === "dark"}
                onCheckedChange={() => setTheme(theme === "dark" ? "light" : "dark")}
                aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
              />
              <Moon className="h-4 w-4" aria-hidden="true" />
            </div>

            <div className="hidden md:flex items-center space-x-2">
              {session ? (
                <Link to="/profile">
                  <Button 
                    variant="outline" 
                    size="sm"
                    aria-label="View profile"
                  >
                    Profile
                  </Button>
                </Link>
              ) : (
                <>
                  <Link to="/auth">
                    <Button 
                      variant="outline" 
                      size="sm"
                      aria-label="Log in to your account"
                    >
                      Log in
                    </Button>
                  </Link>
                  <Link to="/auth">
                    <Button 
                      size="sm"
                      aria-label="Sign up for an account"
                    >
                      Sign up
                    </Button>
                  </Link>
                </>
              )}
            </div>

            <MobileMenu />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
