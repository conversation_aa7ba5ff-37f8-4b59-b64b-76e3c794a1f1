import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Upload, File, Image, Music } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { encryptFile } from "@/utils/encryption";
import { checkRateLimit } from "@/utils/rateLimit";
import { logAuditEvent } from "@/utils/auditLog";

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const ALLOWED_FILE_TYPES = {
  'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  'document': ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
  'audio': ['audio/mpeg', 'audio/wav', 'audio/ogg']
};

const getBucketForFileType = (contentType: string) => {
  if (ALLOWED_FILE_TYPES.image.includes(contentType)) return 'images';
  if (ALLOWED_FILE_TYPES.audio.includes(contentType)) return 'audio';
  return 'documents';
};

export const FileUpload = () => {
  const session = useSession();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [preview, setPreview] = useState<string | null>(null);
  const [folderPath, setFolderPath] = useState('/');
  const [description, setDescription] = useState('');

  const validateFile = (file: File) => {
    if (file.size > MAX_FILE_SIZE) {
      throw new Error("File size exceeds 50MB limit");
    }
    
    const isValidType = Object.values(ALLOWED_FILE_TYPES).flat().includes(file.type);
    if (!isValidType) {
      throw new Error("File type not supported");
    }
    
    return true;
  };

  const getFileTypeIcon = (contentType: string) => {
    if (ALLOWED_FILE_TYPES.image.includes(contentType)) return <Image className="w-5 h-5" />;
    if (ALLOWED_FILE_TYPES.audio.includes(contentType)) return <Music className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0];
      if (!file) return;

      validateFile(file);

      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setPreview(null);
      }

      await handleUpload(file);
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleUpload = async (file: File) => {
    try {
      const isAllowed = await checkRateLimit(session?.user.id!, 'file_upload', 10, 60);
      if (!isAllowed) {
        throw new Error("Upload rate limit exceeded. Please try again later.");
      }

      setUploading(true);
      setProgress(0);

      // Encrypt sensitive file types
      const shouldEncrypt = file.type.includes('document') || file.type.includes('text');
      let fileToUpload = file;
      let encryptionKey = null;

      if (shouldEncrypt) {
        const { encryptedFile, key } = await encryptFile(file);
        fileToUpload = encryptedFile;
        encryptionKey = key;
      }

      const bucket = getBucketForFileType(file.type);
      const fileExt = file.name.split('.').pop();
      const normalizedFolderPath = folderPath.startsWith('/') ? folderPath : `/${folderPath}`;
      const filePath = `${normalizedFolderPath}/${Math.random()}.${fileExt}`;

      const xhr = new XMLHttpRequest();
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          setProgress(progress);
        }
      });

      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, fileToUpload, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      const metadata = {
        originalName: file.name,
        lastModified: file.lastModified,
        type: file.type,
        is_encrypted: shouldEncrypt,
        encryption_metadata: shouldEncrypt ? { key: encryptionKey } : null
      };

      const { error: dbError } = await supabase
        .from('files')
        .insert({
          user_id: session?.user.id,
          filename: file.name,
          file_path: filePath,
          file_type: file.type.split('/')[0],
          size: file.size,
          content_type: file.type,
          folder_path: normalizedFolderPath,
          description: description,
          is_encrypted: shouldEncrypt,
          encryption_metadata: shouldEncrypt ? { key: encryptionKey } : null,
          metadata
        });

      if (dbError) throw dbError;

      await logAuditEvent(session?.user.id!, 'file_upload', 'file', filePath);

      toast({
        title: "Success",
        description: "File uploaded successfully",
      });
      
      setDescription('');
    } catch (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <Input
          placeholder="Folder path (e.g., /documents/work)"
          value={folderPath}
          onChange={(e) => setFolderPath(e.target.value)}
        />
        <Input
          placeholder="File description (optional)"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </div>

      <div className="flex flex-col items-center p-8 border-2 border-dashed rounded-lg hover:border-primary transition-colors">
        <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
        <Button variant="outline" disabled={uploading}>
          <label className="cursor-pointer">
            {uploading ? "Uploading..." : "Select File"}
            <input
              type="file"
              className="hidden"
              onChange={handleFileChange}
              accept={Object.values(ALLOWED_FILE_TYPES).flat().join(',')}
              disabled={uploading}
            />
          </label>
        </Button>
        <p className="mt-2 text-sm text-muted-foreground">
          Max file size: 50MB. Supported formats: Images, Documents, Audio
        </p>
      </div>

      {uploading && (
        <div className="space-y-2">
          <Progress value={progress} />
          <p className="text-sm text-center">{Math.round(progress)}%</p>
        </div>
      )}

      {preview && (
        <div className="mt-4">
          <p className="text-sm font-medium mb-2">Preview:</p>
          <img
            src={preview}
            alt="Preview"
            className="max-w-full h-auto rounded-lg max-h-[200px] object-contain"
          />
        </div>
      )}
    </div>
  );
};
