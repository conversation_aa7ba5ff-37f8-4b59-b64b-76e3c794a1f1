import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Music, Image as ImageIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const EPKGenerator = () => {
  const session = useSession();
  const { toast } = useToast();
  const [generating, setGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState("");
  const [formData, setFormData] = useState({
    artistName: "",
    bio: "",
    genre: ""
  });

  const { data: profile } = useQuery({
    queryKey: ['profile', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session?.user.id)
        .single();
      if (error) throw error;
      return data;
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleGenerate = async () => {
    try {
      setGenerating(true);
      const { data, error } = await supabase.functions.invoke('generate-portfolio-content', {
        body: {
          type: 'epk',
          data: formData
        }
      });

      if (error) throw error;
      setGeneratedContent(data.content);
      
      toast({
        title: "EPK Generated",
        description: "Your Electronic Press Kit has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate EPK. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-semibold mb-4">Electronic Press Kit</h2>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Artist Name</label>
              <Input 
                name="artistName"
                value={formData.artistName}
                onChange={handleInputChange}
                defaultValue={profile?.full_name} 
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Bio</label>
              <Textarea 
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                defaultValue={profile?.introduction} 
                rows={4} 
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Genre</label>
              <Input 
                name="genre"
                value={formData.genre}
                onChange={handleInputChange}
                placeholder="e.g., Rock, Hip-Hop, Electronic" 
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="border-2 border-dashed rounded-lg p-6 text-center">
              <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">
                Drop your press photos here or click to upload
              </p>
            </div>

            <div className="border-2 border-dashed rounded-lg p-6 text-center">
              <Music className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">
                Add featured music tracks
              </p>
            </div>
          </div>
        </div>

        {generatedContent && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Generated EPK Content:</h3>
            <div className="whitespace-pre-wrap">{generatedContent}</div>
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <Button onClick={handleGenerate} disabled={generating}>
            {generating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Generate EPK
          </Button>
        </div>
      </Card>
    </div>
  );
};