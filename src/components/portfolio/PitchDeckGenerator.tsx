import React from "react";
import { useToast } from "@/components/ui/use-toast";
import { useSession } from "@supabase/auth-helpers-react";
import { Card } from "@/components/ui/card";
import { ShareDialog } from "@/components/file/ShareDialog";
import { PitchDeckForm } from "./PitchDeckForm";
import { PitchDeckPreview } from "./PitchDeckPreview";
import { useSubscription } from "@/hooks/useSubscription";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { usePitchDeck } from "@/hooks/usePitchDeck";
import { usePitchDeckActions } from "@/hooks/usePitchDeckActions";

export const PitchDeckGenerator = () => {
  const { toast } = useToast();
  const session = useSession();
  const { data: subscription } = useSubscription();
  const {
    generating,
    generatedContent,
    stakeholder,
    showShareDialog,
    formData,
    downloadCount,
    setGenerating,
    setGeneratedContent,
    setStakeholder,
    setShowShareDialog,
    handleInputChange,
  } = usePitchDeck();

  const { handleGenerate, handleDownload } = usePitchDeckActions();

  const getPlanLimits = () => {
    switch (subscription?.plan) {
      case 'scale':
      case 'enterprise':
        return { downloads: 999999, sharing: true, generation: true };
      case 'growth':
        return { downloads: 50, sharing: true, generation: true };
      case 'starter':
        return { downloads: 10, sharing: true, generation: true };
      default: // free plan
        return { downloads: 2, sharing: false, generation: true };
    }
  };

  const planLimits = getPlanLimits();

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-semibold mb-6">Pitch Deck Generator</h2>
        
        {subscription?.plan === 'free' && (
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You're on the free plan. Upgrade to unlock more downloads and sharing capabilities.
            </AlertDescription>
          </Alert>
        )}
        
        <PitchDeckForm
          formData={formData}
          stakeholder={stakeholder}
          generating={generating}
          onInputChange={handleInputChange}
          onStakeholderChange={setStakeholder}
          onGenerate={() => handleGenerate({
            formData,
            stakeholder,
            setGenerating,
            setGeneratedContent,
            planLimits,
          })}
        />

        <PitchDeckPreview
          content={generatedContent}
          downloadCount={downloadCount}
          onShare={() => {
            if (!planLimits.sharing) {
              toast({
                title: "Feature not available",
                description: "Upgrade your plan to access sharing capabilities.",
                variant: "destructive",
              });
              return;
            }
            setShowShareDialog(true);
          }}
          onDownload={() => handleDownload({
            downloadCount,
            planLimits,
            session,
          })}
        />
      </Card>

      {generatedContent && (
        <ShareDialog
          file={{
            id: 'pitch-deck',
            filename: 'Pitch Deck',
            content_type: 'application/pdf',
            file_path: '',
            user_id: session?.user.id || '',
            size: 0,
            created_at: new Date().toISOString(),
          }}
          open={showShareDialog}
          onOpenChange={setShowShareDialog}
        />
      )}
    </div>
  );
};