import React from "react";
import { Button } from "@/components/ui/button";
import { Download, Share2 } from "lucide-react";

interface PitchDeckPreviewProps {
  content: string;
  downloadCount: number | undefined;
  onShare: () => void;
  onDownload: () => void;
}

export const PitchDeckPreview = ({
  content,
  downloadCount,
  onShare,
  onDownload,
}: PitchDeckPreviewProps) => {
  if (!content) return null;

  return (
    <div id="pitch-deck-content" className="mt-6 p-6 bg-card border rounded-lg space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Generated Pitch Deck:</h3>
        <div className="space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={onShare}
          >
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button 
            variant="default" 
            size="sm"
            onClick={onDownload}
            disabled={downloadCount && downloadCount >= 10}
          >
            <Download className="w-4 h-4 mr-2" />
            Download PDF
            {downloadCount && downloadCount > 0 && ` (${10 - downloadCount} left)`}
          </Button>
        </div>
      </div>
      <div className="whitespace-pre-wrap prose dark:prose-invert max-w-none">
        {content}
      </div>
    </div>
  );
};