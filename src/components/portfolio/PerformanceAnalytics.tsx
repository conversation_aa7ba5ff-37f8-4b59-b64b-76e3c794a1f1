import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from "recharts";

const mockData = [
  { month: "Jan", streams: 1200, engagement: 75 },
  { month: "Feb", streams: 1900, engagement: 82 },
  { month: "Mar", streams: 2400, engagement: 88 },
  { month: "Apr", streams: 3100, engagement: 85 },
  { month: "May", streams: 3800, engagement: 90 },
  { month: "Jun", streams: 4200, engagement: 92 },
];

export const PerformanceAnalytics = () => {
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-semibold mb-6">Performance Analytics</h2>
        
        <div className="grid md:grid-cols-3 gap-4 mb-8">
          <Card className="p-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              Total Streams
            </h3>
            <p className="text-2xl font-bold mt-2">16.6K</p>
          </Card>
          
          <Card className="p-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              Avg. Engagement Rate
            </h3>
            <p className="text-2xl font-bold mt-2">85.3%</p>
          </Card>
          
          <Card className="p-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              Growth Rate
            </h3>
            <p className="text-2xl font-bold mt-2">+28.4%</p>
          </Card>
        </div>

        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="streams"
                stroke="#2563eb"
                strokeWidth={2}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="engagement"
                stroke="#16a34a"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>
  );
};