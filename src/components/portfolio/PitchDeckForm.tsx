import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface PitchDeckFormProps {
  formData: {
    achievements: string;
    targetMarket: string;
    uniqueValue: string;
  };
  stakeholder: string;
  generating: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onStakeholderChange: (value: string) => void;
  onGenerate: () => void;
}

export const PitchDeckForm = ({
  formData,
  stakeholder,
  generating,
  onInputChange,
  onStakeholderChange,
  onGenerate,
}: PitchDeckFormProps) => {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Target Stakeholder</Label>
        <Select value={stakeholder} onValueChange={onStakeholderChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select stakeholder type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="label">Record Label</SelectItem>
            <SelectItem value="investor">Investor</SelectItem>
            <SelectItem value="sponsor">Sponsor</SelectItem>
            <SelectItem value="venue">Venue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Key Achievements</Label>
        <Textarea 
          name="achievements"
          value={formData.achievements}
          onChange={onInputChange}
          placeholder="List your major achievements, awards, or milestones"
          rows={4}
        />
      </div>

      <div className="space-y-2">
        <Label>Target Market</Label>
        <Input 
          name="targetMarket"
          value={formData.targetMarket}
          onChange={onInputChange}
          placeholder="Describe your target audience" 
        />
      </div>

      <div className="space-y-2">
        <Label>Unique Value Proposition</Label>
        <Textarea 
          name="uniqueValue"
          value={formData.uniqueValue}
          onChange={onInputChange}
          placeholder="What makes you stand out from other artists?"
          rows={4}
        />
      </div>

      <div className="flex justify-end">
        <Button onClick={onGenerate} disabled={generating}>
          {generating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Generate Pitch Deck
        </Button>
      </div>
    </div>
  );
};