import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export function MatchingHistory() {
  const session = useSession();

  const { data: history } = useQuery({
    queryKey: ['matching-history', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('matching_history')
        .select(`
          *,
          matched_profiles:profiles!matched_entity_id(*)
        `)
        .eq('artist_id', session?.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Match History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {history?.map((match) => (
            <div
              key={match.id}
              className="flex items-center justify-between p-4 rounded-lg border"
            >
              <div className="flex items-center gap-3">
                {match.match_type === 'label' ? (
                  <Building className="h-5 w-5" />
                ) : (
                  <Users className="h-5 w-5" />
                )}
                <div>
                  <p className="font-medium">
                    {match.matched_profiles.full_name || 'Unnamed Match'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Match Score: {Math.round(match.match_score * 100)}%
                  </p>
                </div>
              </div>
              <span className="text-sm text-muted-foreground">
                {new Date(match.created_at).toLocaleDateString()}
              </span>
            </div>
          ))}

          {!history?.length && (
            <p className="text-center text-muted-foreground py-4">
              No matching history available.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}