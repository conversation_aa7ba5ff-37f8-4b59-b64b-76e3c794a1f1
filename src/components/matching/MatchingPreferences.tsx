import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MultiSelect } from "@/components/ui/multi-select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

const GENRES = [
  "Pop", "Rock", "Hip Hop", "R&B", "Jazz", "Classical", "Electronic", 
  "Folk", "Country", "Metal", "Blues", "Reggae", "World Music"
];

const CAREER_STAGES = [
  "Emerging", "Developing", "Established", "Professional"
];

const COLLABORATION_INTERESTS = [
  "Songwriting", "Production", "Live Performance", "Music Video",
  "Studio Session", "Touring", "Remixing", "Feature Vocals"
];

export function MatchingPreferences() {
  const session = useSession();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);

  const { data: preferences } = useQuery({
    queryKey: ['artist-preferences', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('artist_preferences')
        .select('*')
        .eq('user_id', session?.user.id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });

  const updatePreferences = useMutation({
    mutationFn: async (values: any) => {
      const { data, error } = await supabase
        .from('artist_preferences')
        .upsert({
          user_id: session?.user.id,
          ...values
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['artist-preferences'] });
      toast({
        title: "Preferences updated",
        description: "Your matching preferences have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update preferences. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    
    const values = {
      genres: preferences?.genres || [],
      collaboration_interests: preferences?.collaboration_interests || [],
      career_stage: formData.get('career_stage') as string,
      target_labels: (formData.get('target_labels') as string)
        .split(',')
        .map(label => label.trim())
        .filter(Boolean),
    };

    updatePreferences.mutate(values);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Matching Preferences</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label>Genres</Label>
            <MultiSelect
              options={GENRES}
              selected={preferences?.genres || []}
              onChange={(value) => 
                updatePreferences.mutate({ ...preferences, genres: value })
              }
              placeholder="Select genres"
            />
          </div>

          <div className="space-y-2">
            <Label>Career Stage</Label>
            <select
              name="career_stage"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={preferences?.career_stage || ''}
            >
              <option value="">Select a stage</option>
              {CAREER_STAGES.map((stage) => (
                <option key={stage} value={stage}>{stage}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <Label>Collaboration Interests</Label>
            <MultiSelect
              options={COLLABORATION_INTERESTS}
              selected={preferences?.collaboration_interests || []}
              onChange={(value) =>
                updatePreferences.mutate({ 
                  ...preferences, 
                  collaboration_interests: value 
                })
              }
              placeholder="Select interests"
            />
          </div>

          <div className="space-y-2">
            <Label>Target Labels</Label>
            <Input
              name="target_labels"
              placeholder="Enter target labels (comma-separated)"
              defaultValue={preferences?.target_labels?.join(', ') || ''}
            />
          </div>

          <Button type="submit" disabled={loading}>
            Save Preferences
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}