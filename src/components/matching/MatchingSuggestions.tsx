import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building, Music, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export function MatchingSuggestions() {
  const session = useSession();

  const { data: matches } = useQuery({
    queryKey: ['matches', session?.user.id],
    queryFn: async () => {
      const { data: matchHistory, error } = await supabase
        .from('matching_history')
        .select(`
          *,
          matched_profiles:profiles!matched_entity_id(*)
        `)
        .eq('artist_id', session?.user.id)
        .order('match_score', { ascending: false })
        .limit(5);

      if (error) throw error;
      return matchHistory;
    },
    enabled: !!session?.user.id,
  });

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {matches?.map((match) => (
        <Card key={match.id} className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {match.match_type === 'label' ? (
                <Building className="h-5 w-5" />
              ) : (
                <Users className="h-5 w-5" />
              )}
              {match.matched_profiles.full_name || 'Unnamed Match'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Match Score: {Math.round(match.match_score * 100)}%
              </p>
              <Button variant="outline" className="w-full">
                View Profile
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {!matches?.length && (
        <Card className="col-span-full">
          <CardContent className="p-6 text-center">
            <Music className="mx-auto h-12 w-12 text-muted-foreground" />
            <p className="mt-2 text-muted-foreground">
              No matches found. Update your preferences to get started!
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}