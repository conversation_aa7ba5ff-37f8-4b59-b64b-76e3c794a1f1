import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Film, Tv, DollarSign } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const LicensingMatches = () => {
  const session = useSession();
  const { toast } = useToast();

  const { data: matches } = useQuery({
    queryKey: ['licensing-matches', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('licensing_opportunities')
        .select('*')
        .eq('user_id', session?.user.id);
      if (error) throw error;
      return data;
    },
  });

  const handleAnalyze = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('analyze-licensing-opportunities', {
        body: { userId: session?.user.id }
      });

      if (error) throw error;

      toast({
        title: "Analysis Complete",
        description: "New licensing opportunities have been found",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to analyze licensing opportunities",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-semibold">Potential Matches</h3>
            <p className="text-sm text-muted-foreground">
              AI-powered matches for your music
            </p>
          </div>
          <Button onClick={handleAnalyze}>
            Find New Opportunities
          </Button>
        </div>

        <div className="space-y-4">
          {matches?.map((match) => (
            <Card key={match.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    {match.potential_matches?.[0]?.type === 'film' ? (
                      <Film className="w-4 h-4" />
                    ) : match.potential_matches?.[0]?.type === 'tv' ? (
                      <Tv className="w-4 h-4" />
                    ) : (
                      <DollarSign className="w-4 h-4" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {match.song_metadata.title}
                    </h4>
                    <div className="flex gap-2 mt-1">
                      <Badge>{match.song_metadata.genre}</Badge>
                      <Badge variant="outline">
                        {match.potential_matches?.[0]?.confidence}% Match
                      </Badge>
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  View Details
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </Card>
    </div>
  );
};