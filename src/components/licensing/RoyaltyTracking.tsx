import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { DollarSign, TrendingUp, Calendar } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const RoyaltyTracking = () => {
  const session = useSession();

  const { data: royalties } = useQuery({
    queryKey: ['licensing-royalties', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('licensing_opportunities')
        .select('*')
        .eq('user_id', session?.user.id);
      if (error) throw error;
      return data;
    },
  });

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <DollarSign className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Revenue</p>
              <h3 className="text-2xl font-bold">$12,450</h3>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <TrendingUp className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Growth Rate</p>
              <h3 className="text-2xl font-bold">+24%</h3>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Calendar className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Active Licenses</p>
              <h3 className="text-2xl font-bold">8</h3>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Revenue by License Type</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Film Sync</span>
              <span className="font-semibold">$5,200</span>
            </div>
            <Progress value={45} />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>TV Shows</span>
              <span className="font-semibold">$4,800</span>
            </div>
            <Progress value={40} />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Advertising</span>
              <span className="font-semibold">$2,450</span>
            </div>
            <Progress value={15} />
          </div>
        </div>
      </Card>
    </div>
  );
};