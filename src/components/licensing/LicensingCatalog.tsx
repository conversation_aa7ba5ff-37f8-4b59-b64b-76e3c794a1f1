import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Music, Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const LicensingCatalog = () => {
  const session = useSession();
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);

  const { data: catalog, refetch } = useQuery({
    queryKey: ['licensing-catalog', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('licensing_opportunities')
        .select('*')
        .eq('user_id', session?.user.id);
      if (error) throw error;
      return data;
    },
  });

  const handleUpload = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUploading(true);

    try {
      const formData = new FormData(e.currentTarget);
      const title = formData.get('title') as string;
      const genre = formData.get('genre') as string;
      const description = formData.get('description') as string;

      const { error } = await supabase.from('licensing_opportunities').insert({
        user_id: session?.user.id,
        song_metadata: { title, genre, description },
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Song added to your licensing catalog",
      });

      refetch();
      (e.target as HTMLFormElement).reset();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add song to catalog",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <form onSubmit={handleUpload} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Song Title</Label>
            <Input id="title" name="title" required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="genre">Genre</Label>
            <Input id="genre" name="genre" required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description" 
              name="description" 
              placeholder="Describe your song and its potential use cases..."
            />
          </div>

          <Button type="submit" disabled={uploading}>
            {uploading ? (
              <>Uploading...</>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Add to Catalog
              </>
            )}
          </Button>
        </form>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {catalog?.map((item) => (
          <Card key={item.id} className="p-4">
            <div className="flex items-start space-x-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Music className="w-4 h-4" />
              </div>
              <div>
                <h3 className="font-semibold">
                  {item.song_metadata.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {item.song_metadata.genre}
                </p>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};