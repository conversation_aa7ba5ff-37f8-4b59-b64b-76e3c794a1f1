import { Card } from "@/components/ui/card";

interface ArrangementFeedbackProps {
  feedback: {
    structure: string;
    suggestions: string[];
    strengths: string[];
  };
}

export const ArrangementFeedback = ({ feedback }: ArrangementFeedbackProps) => {
  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <h3 className="font-medium">Song Structure</h3>
        <p className="text-sm text-muted-foreground">{feedback.structure}</p>
      </div>
      
      <div className="space-y-2">
        <h3 className="font-medium">Suggestions</h3>
        <ul className="space-y-1">
          {feedback.suggestions.map((suggestion, index) => (
            <li key={index} className="text-sm text-muted-foreground">
              • {suggestion}
            </li>
          ))}
        </ul>
      </div>

      <div className="space-y-2">
        <h3 className="font-medium">Strengths</h3>
        <ul className="space-y-1">
          {feedback.strengths.map((strength, index) => (
            <li key={index} className="text-sm text-muted-foreground">
              • {strength}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};