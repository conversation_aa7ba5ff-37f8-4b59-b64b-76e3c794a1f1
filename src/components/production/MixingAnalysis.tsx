import { Card } from "@/components/ui/card";

interface MixingAnalysisProps {
  analysis: {
    eq: string;
    compression: string;
    balance: string;
  };
}

export const MixingAnalysis = ({ analysis }: MixingAnalysisProps) => {
  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <h3 className="font-medium">EQ Suggestions</h3>
        <p className="text-sm text-muted-foreground">{analysis.eq}</p>
      </div>
      <div className="space-y-2">
        <h3 className="font-medium">Compression</h3>
        <p className="text-sm text-muted-foreground">{analysis.compression}</p>
      </div>
      <div className="space-y-2">
        <h3 className="font-medium">Balance</h3>
        <p className="text-sm text-muted-foreground">{analysis.balance}</p>
      </div>
    </div>
  );
};