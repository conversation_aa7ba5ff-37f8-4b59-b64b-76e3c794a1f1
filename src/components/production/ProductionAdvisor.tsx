import { useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { AudioWaveform, Settings, Users, Layout } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { MixingAnalysis } from "./MixingAnalysis";
import { MusicianRecommendations } from "./MusicianRecommendations";
import { ArrangementFeedback } from "./ArrangementFeedback";

export const ProductionAdvisor = () => {
  const session = useSession();
  const { toast } = useToast();
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);

  const { data: audioFiles } = useQuery({
    queryKey: ['audioFiles', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('files')
        .select('*')
        .eq('user_id', session?.user.id)
        .eq('file_type', 'audio')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  const { data: analysis, refetch: refetchAnalysis } = useQuery({
    queryKey: ['analysis', selectedFileId],
    queryFn: async () => {
      if (!selectedFileId) return null;

      const { data: fileData } = await supabase
        .storage
        .from('audio')
        .createSignedUrl(selectedFileId, 3600);

      const response = await supabase.functions.invoke('analyze-audio', {
        body: { fileId: selectedFileId, audioUrl: fileData?.signedUrl }
      });

      if (response.error) throw response.error;
      return response.data.analysis;
    },
    enabled: !!selectedFileId,
  });

  const handleAnalyze = async (fileId: string) => {
    setSelectedFileId(fileId);
    toast({
      title: "Analysis Started",
      description: "We're analyzing your track. This may take a moment.",
    });
    await refetchAnalysis();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">AI Music Production Advisor</h1>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">Your Audio Files</h2>
          <div className="space-y-4">
            {audioFiles?.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <AudioWaveform className="w-5 h-5" />
                  <span>{file.filename}</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAnalyze(file.id)}
                >
                  Analyze
                </Button>
              </div>
            ))}
            {(!audioFiles || audioFiles.length === 0) && (
              <p className="text-muted-foreground text-center py-8">
                No audio files found. Upload some audio files to get started!
              </p>
            )}
          </div>
        </Card>

        {analysis && (
          <Card className="p-4">
            <Tabs defaultValue="mixing" className="w-full">
              <TabsList className="grid grid-cols-3 w-full">
                <TabsTrigger value="mixing" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Mixing
                </TabsTrigger>
                <TabsTrigger value="musicians" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Musicians
                </TabsTrigger>
                <TabsTrigger value="arrangement" className="flex items-center gap-2">
                  <Layout className="w-4 h-4" />
                  Arrangement
                </TabsTrigger>
              </TabsList>
              <TabsContent value="mixing">
                <MixingAnalysis analysis={analysis.mixing} />
              </TabsContent>
              <TabsContent value="musicians">
                <MusicianRecommendations musicians={analysis.musicians} />
              </TabsContent>
              <TabsContent value="arrangement">
                <ArrangementFeedback feedback={analysis.arrangement} />
              </TabsContent>
            </Tabs>
          </Card>
        )}
      </div>
    </div>
  );
};