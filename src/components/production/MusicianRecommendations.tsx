import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface Musician {
  instrument: string;
  style: string;
  experience: string;
  rate: string;
  match: number;
}

interface MusicianRecommendationsProps {
  musicians: Musician[];
}

export const MusicianRecommendations = ({ musicians }: MusicianRecommendationsProps) => {
  return (
    <div className="space-y-4 p-4">
      {musicians.map((musician, index) => (
        <Card key={index} className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium">{musician.instrument}</h3>
              <p className="text-sm text-muted-foreground">{musician.style}</p>
              <p className="text-sm">Experience: {musician.experience}</p>
              <p className="text-sm">Rate: {musician.rate}</p>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium text-green-600">
                {musician.match}% match
              </div>
              <Button variant="outline" size="sm" className="mt-2">
                Contact
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};