import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { FileUp, FileDown } from "lucide-react";

export const StorageActivityList = () => {
  const session = useSession();

  const { data: recentFiles } = useQuery({
    queryKey: ["recent-files", session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("files")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(5);
      
      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentFiles?.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-2 rounded-lg hover:bg-accent/50"
            >
              <div className="flex items-center gap-3">
                <FileUp className="w-4 h-4 text-primary" />
                <div>
                  <p className="font-medium">{file.filename}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(file.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <span className="text-sm text-muted-foreground">
                {formatBytes(file.size)}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};