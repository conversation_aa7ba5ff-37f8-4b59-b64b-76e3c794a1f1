import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";

export const StorageQuotaCard = () => {
  const session = useSession();

  const { data: analytics } = useQuery({
    queryKey: ["storage-analytics", session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("storage_analytics")
        .select("*")
        .eq("user_id", session?.user.id)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const usagePercentage = analytics 
    ? (analytics.storage_used / analytics.quota_limit) * 100
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Storage Usage</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Progress value={usagePercentage} />
        <div className="flex justify-between text-sm">
          <span>Used: {formatBytes(analytics?.storage_used || 0)}</span>
          <span>Total: {formatBytes(analytics?.quota_limit || 0)}</span>
        </div>
        <div className="grid grid-cols-2 gap-4 pt-4">
          <div>
            <p className="text-sm text-muted-foreground">Total Uploads</p>
            <p className="text-2xl font-bold">{analytics?.total_uploads || 0}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Total Downloads</p>
            <p className="text-2xl font-bold">{analytics?.total_downloads || 0}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};