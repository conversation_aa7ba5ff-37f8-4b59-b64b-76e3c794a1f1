import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis, ResponsiveContainer } from "recharts";

export const StorageUsageChart = () => {
  const session = useSession();

  const { data: files } = useQuery({
    queryKey: ["files-timeline", session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("files")
        .select("created_at, size")
        .order("created_at", { ascending: true });
      
      if (error) throw error;

      // Aggregate data by day
      const aggregatedData = data.reduce((acc: any[], file) => {
        const date = new Date(file.created_at).toLocaleDateString();
        const existingDay = acc.find(d => d.date === date);
        
        if (existingDay) {
          existingDay.size += file.size;
        } else {
          acc.push({ date, size: file.size });
        }
        
        return acc;
      }, []);

      return aggregatedData;
    },
    enabled: !!session?.user.id,
  });

  const formatBytes = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Storage Usage Over Time</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[200px]">
          <ChartContainer
            config={{
              size: {
                color: "var(--primary)",
              },
            }}
          >
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={files}>
                <defs>
                  <linearGradient id="colorSize" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="var(--primary)" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="var(--primary)" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <XAxis
                  dataKey="date"
                  stroke="var(--muted-foreground)"
                  fontSize={12}
                />
                <YAxis
                  tickFormatter={formatBytes}
                  stroke="var(--muted-foreground)"
                  fontSize={12}
                />
                <ChartTooltip />
                <Area
                  type="monotone"
                  dataKey="size"
                  stroke="var(--primary)"
                  fillOpacity={1}
                  fill="url(#colorSize)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
};