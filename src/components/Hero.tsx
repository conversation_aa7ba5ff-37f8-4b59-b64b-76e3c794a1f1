import { But<PERSON> } from "@/components/ui/button";
import { Music, Users, Award } from "lucide-react";
import { LazyImage } from "@/components/ui/lazy-image";

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-primary/10 to-secondary/10 py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            Transform the Music Industry with AI
          </h1>
          <p className="text-lg md:text-xl text-gray-600 mb-6 animate-fadeIn animate-delay-100">
            The ultimate AI-powered platform connecting artists, professionals, and businesses across the world's music ecosystem
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-10 animate-fadeIn animate-delay-200">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Get Started Free
            </Button>
            <Button size="lg" variant="outline">
              Explore Directory
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 animate-fadeIn animate-delay-300">
            <div className="flex flex-col items-center">
              <LazyImage
                src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f"
                srcSet="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=640 640w, 
                        https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=1080 1080w, 
                        https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=1920 1920w"
                sizes="(max-width: 640px) 100vw, (max-width: 1080px) 50vw, 33vw"
                alt="Singer performing with microphone"
                className="w-full h-48 object-cover rounded-lg mb-4"
              />
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Music className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">100,000+ Artists</h3>
              <p className="text-gray-600">Connect with talented musicians worldwide</p>
            </div>
            <div className="flex flex-col items-center">
              <LazyImage
                src="https://images.unsplash.com/photo-1459749411175-04bf5292ceea"
                srcSet="https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=640 640w, 
                        https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=1080 1080w, 
                        https://images.unsplash.com/photo-1459749411175-04bf5292ceea?w=1920 1920w"
                sizes="(max-width: 640px) 100vw, (max-width: 1080px) 50vw, 33vw"
                alt="Crowd at music concert"
                className="w-full h-48 object-cover rounded-lg mb-4"
              />
              <div className="w-12 h-12 rounded-full bg-secondary/10 flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-secondary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">50,000+ Professionals</h3>
              <p className="text-gray-600">Industry experts ready to collaborate</p>
            </div>
            <div className="flex flex-col items-center">
              <LazyImage
                src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d"
                srcSet="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=640 640w, 
                        https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=1080 1080w, 
                        https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=1920 1920w"
                sizes="(max-width: 640px) 100vw, (max-width: 1080px) 50vw, 33vw"
                alt="Music industry professionals meeting"
                className="w-full h-48 object-cover rounded-lg mb-4"
              />
              <div className="w-12 h-12 rounded-full bg-accent/10 flex items-center justify-center mb-4">
                <Award className="w-6 h-6 text-accent" />
              </div>
              <h3 className="text-xl font-semibold mb-2">5,000+ Organizations</h3>
              <p className="text-gray-600">Leading music industry organizations</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;