export type SocialLinks = {
  twitter?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  [key: string]: string | undefined;
};

export type PortfolioItem = {
  title: string;
  description: string;
  imageUrl: string;
  link?: string;
};

export type Profile = {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  introduction: string | null;
  updated_at: string;
  created_at: string;
  cover_photo_url: string | null;
  social_links: SocialLinks;
  portfolio_items: PortfolioItem[];
  primary_role: string | null;
  secondary_roles: string[] | null;
  industry_sectors: string[] | null;
  specializations: string[] | null;
  regions: string[] | null;
  languages: string[] | null;
};