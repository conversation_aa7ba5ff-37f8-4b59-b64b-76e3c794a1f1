import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { toast } from "@/components/ui/use-toast";

export const useProfileImages = (userId?: string) => {
  const session = useSession();
  const currentUserId = userId || session?.user.id;

  return useQuery({
    queryKey: ['profileImages', currentUserId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profile_images')
        .select('*')
        .eq('user_id', currentUserId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!currentUserId,
  });
};

export const useUploadProfileImage = () => {
  const queryClient = useQueryClient();
  const session = useSession();

  return useMutation({
    mutationFn: async ({ file, isPrimary = false }: { file: File; isPrimary?: boolean }) => {
      if (!session?.user.id) throw new Error('No user session');

      const fileExt = file.name.split('.').pop();
      const filePath = `${session.user.id}/${Math.random()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      const { error: dbError } = await supabase
        .from('profile_images')
        .insert({
          user_id: session.user.id,
          image_url: publicUrl,
          is_primary: isPrimary,
        });

      if (dbError) throw dbError;

      if (isPrimary) {
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ avatar_url: publicUrl })
          .eq('id', session.user.id);

        if (updateError) throw updateError;
      }

      return publicUrl;
    },
    onSuccess: () => {
      if (session?.user.id) {
        queryClient.invalidateQueries({ queryKey: ['profileImages', session.user.id] });
        queryClient.invalidateQueries({ queryKey: ['profile', session.user.id] });
      }
      toast({
        title: "Success",
        description: "Profile image uploaded successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to upload profile image",
        variant: "destructive",
      });
    },
  });
};