import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { toast } from "@/components/ui/use-toast";
import { Profile } from "@/types/profile";

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const session = useSession();

  return useMutation({
    mutationFn: async (updates: Partial<Profile>) => {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', session?.user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onMutate: async (newProfile) => {
      const userId = session?.user.id;
      if (!userId) return;

      await queryClient.cancelQueries({ queryKey: ['profile', userId] });
      const previousProfile = queryClient.getQueryData(['profile', userId]);

      queryClient.setQueryData(['profile', userId], (old: Profile) => ({
        ...old,
        ...newProfile,
      }));

      return { previousProfile };
    },
    onError: (err, newProfile, context) => {
      if (context?.previousProfile && session?.user.id) {
        queryClient.setQueryData(['profile', session.user.id], context.previousProfile);
      }
      toast({
        title: "Error updating profile",
        description: "Your profile couldn't be updated. Please try again.",
        variant: "destructive",
      });
    },
    onSuccess: () => {
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });
    },
    onSettled: () => {
      if (session?.user.id) {
        queryClient.invalidateQueries({ queryKey: ['profile', session.user.id] });
      }
    },
  });
};