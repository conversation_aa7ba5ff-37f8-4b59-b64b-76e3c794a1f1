import { useQueryClient, useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { FileRow } from "@/integrations/supabase/types/database";
import { useSession } from "@supabase/auth-helpers-react";
import { useToast } from "@/components/ui/use-toast";

export const useFileOperations = () => {
  const session = useSession();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const deleteFile = useMutation({
    mutationFn: async (file: FileRow) => {
      const { error } = await supabase
        .from('files')
        .update({
          deleted_at: new Date().toISOString(),
          deleted_by: session?.user.id
        })
        .eq('id', file.id);

      if (error) throw error;
      return file;
    },
    onMutate: async (file) => {
      const queryKey = ['files', session?.user.id];
      await queryClient.cancelQueries({ queryKey });

      const previousFiles = queryClient.getQueryData(queryKey);

      queryClient.setQueryData<FileRow[]>(queryKey, (old) => 
        old?.map(f => f.id === file.id 
          ? { ...f, deleted_at: new Date().toISOString(), deleted_by: session?.user.id }
          : f
        )
      );

      return { previousFiles };
    },
    onError: (err, file, context) => {
      if (context?.previousFiles) {
        queryClient.setQueryData(['files', session?.user.id], context.previousFiles);
      }
      toast({
        title: "Error",
        description: "Failed to delete file",
        variant: "destructive",
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "File moved to trash",
      });
    },
  });

  const restoreFile = useMutation({
    mutationFn: async (file: FileRow) => {
      const { error } = await supabase
        .from('files')
        .update({
          deleted_at: null,
          deleted_by: null
        })
        .eq('id', file.id);

      if (error) throw error;
      return file;
    },
    onMutate: async (file) => {
      const queryKey = ['files', session?.user.id];
      await queryClient.cancelQueries({ queryKey });

      const previousFiles = queryClient.getQueryData(queryKey);

      queryClient.setQueryData<FileRow[]>(queryKey, (old) => 
        old?.map(f => f.id === file.id 
          ? { ...f, deleted_at: null, deleted_by: null }
          : f
        )
      );

      return { previousFiles };
    },
    onError: (err, file, context) => {
      if (context?.previousFiles) {
        queryClient.setQueryData(['files', session?.user.id], context.previousFiles);
      }
      toast({
        title: "Error",
        description: "Failed to restore file",
        variant: "destructive",
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "File restored successfully",
      });
    },
  });

  return {
    deleteFile,
    restoreFile,
  };
};