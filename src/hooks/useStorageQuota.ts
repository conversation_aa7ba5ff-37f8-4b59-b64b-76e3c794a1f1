import { useQuery } from "@tanstack/react-query";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";

export const useStorageQuota = () => {
  const session = useSession();

  return useQuery({
    queryKey: ["storage-quota", session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("storage_analytics")
        .select("storage_used, quota_limit")
        .eq("user_id", session?.user.id)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!session?.user.id,
  });
};