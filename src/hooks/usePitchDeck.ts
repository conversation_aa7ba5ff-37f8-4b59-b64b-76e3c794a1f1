import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useSession } from "@supabase/auth-helpers-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface PitchDeckFormData {
  achievements: string;
  targetMarket: string;
  uniqueValue: string;
}

export const usePitchDeck = () => {
  const { toast } = useToast();
  const session = useSession();
  const [generating, setGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState("");
  const [stakeholder, setStakeholder] = useState("label");
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [formData, setFormData] = useState<PitchDeckFormData>({
    achievements: "",
    targetMarket: "",
    uniqueValue: ""
  });

  const { data: downloadCount } = useQuery({
    queryKey: ['pitch-deck-downloads', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pitch_deck_downloads')
        .select('download_count')
        .eq('user_id', session?.user.id)
        .eq('download_date', new Date().toISOString().split('T')[0])
        .single();
      
      if (error && error.code !== 'PGRST116') return 0;
      return data?.download_count || 0;
    },
    enabled: !!session?.user.id,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return {
    generating,
    generatedContent,
    stakeholder,
    showShareDialog,
    formData,
    downloadCount,
    setGenerating,
    setGeneratedContent,
    setStakeholder,
    setShowShareDialog,
    handleInputChange,
  };
};