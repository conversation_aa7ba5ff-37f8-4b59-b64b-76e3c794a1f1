import { useToast } from "@/components/ui/use-toast";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { PlanLimits } from "@/types/subscription";

interface GenerateParams {
  formData: {
    achievements: string;
    targetMarket: string;
    uniqueValue: string;
  };
  stakeholder: string;
  setGenerating: (value: boolean) => void;
  setGeneratedContent: (value: string) => void;
  planLimits: PlanLimits;
}

interface DownloadParams {
  downloadCount: number | undefined;
  planLimits: PlanLimits;
  session: any;
}

export const usePitchDeckActions = () => {
  const { toast } = useToast();
  const session = useSession();

  const handleGenerate = async ({
    formData,
    stakeholder,
    setGenerating,
    setGeneratedContent,
    planLimits,
  }: GenerateParams) => {
    if (!planLimits.generation) {
      toast({
        title: "Feature not available",
        description: "Upgrade your plan to access pitch deck generation.",
        variant: "destructive",
      });
      return;
    }

    try {
      setGenerating(true);
      const { data, error } = await supabase.functions.invoke('generate-portfolio-content', {
        body: {
          type: 'pitch',
          data: {
            ...formData,
            stakeholder
          }
        }
      });

      if (error) throw error;
      setGeneratedContent(data.content);
      
      toast({
        title: "Pitch Deck Generated",
        description: "Your personalized pitch deck has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate pitch deck. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async ({ downloadCount, planLimits, session }: DownloadParams) => {
    if (!session?.user.id) {
      toast({
        title: "Error",
        description: "Please sign in to download pitch decks.",
        variant: "destructive",
      });
      return;
    }

    if (downloadCount >= planLimits.downloads) {
      toast({
        title: "Download Limit Reached",
        description: `You've reached your daily download limit of ${planLimits.downloads} pitch decks.`,
        variant: "destructive",
      });
      return;
    }

    try {
      const element = document.getElementById('pitch-deck-content');
      if (!element) return;

      const canvas = await html2canvas(element);
      const pdf = new jsPDF();
      
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 10, 10, 190, 0);
      pdf.save('pitch-deck.pdf');

      const { error } = await supabase
        .from('pitch_deck_downloads')
        .upsert({
          user_id: session.user.id,
          download_date: new Date().toISOString().split('T')[0],
          download_count: (downloadCount || 0) + 1
        });

      if (error) throw error;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download pitch deck. Please try again.",
        variant: "destructive",
      });
    }
  };

  return {
    handleGenerate,
    handleDownload,
  };
};