import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";

export const useSubscription = () => {
  const session = useSession();

  return useQuery({
    queryKey: ['subscription', session?.user.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', session?.user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return { plan: 'free' };
        throw error;
      }

      return data;
    },
    enabled: !!session?.user.id,
  });
};