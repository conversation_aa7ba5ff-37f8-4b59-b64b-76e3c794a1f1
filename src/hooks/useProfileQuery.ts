import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useSession } from "@supabase/auth-helpers-react";
import { Profile } from "@/types/profile";

export const useProfileQuery = (userId?: string) => {
  const session = useSession();
  const currentUserId = userId || session?.user.id;

  return useQuery({
    queryKey: ['profile', currentUserId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', currentUserId)
        .single();

      if (error) throw error;
      return data as Profile;
    },
    enabled: !!currentUserId,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 30,
  });
};