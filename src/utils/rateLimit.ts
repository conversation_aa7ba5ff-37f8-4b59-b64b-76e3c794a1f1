import { supabase } from "@/integrations/supabase/client";

export const checkRateLimit = async (
  userId: string,
  actionType: string,
  maxAttempts: number = 5,
  windowMinutes: number = 15
): Promise<boolean> => {
  const { data, error } = await supabase.rpc('check_rate_limit', {
    user_id: userId,
    action_type: actionType,
    max_attempts: maxAttempts,
    window_minutes: windowMinutes
  });

  if (error) throw error;
  return data;
};