import { supabase } from "@/integrations/supabase/client";

export const logAuditEvent = async (
  userId: string,
  action: string,
  resourceType: string,
  resourceId?: string
) => {
  try {
    const { data: { publicUrl: ipData } } = await fetch('https://api.ipify.org?format=json');
    const userAgent = window.navigator.userAgent;

    const { error } = await supabase.rpc('log_audit_event', {
      p_user_id: userId,
      p_action: action,
      p_resource_type: resourceType,
      p_resource_id: resourceId,
      p_ip_address: ipData?.ip,
      p_user_agent: userAgent
    });

    if (error) throw error;
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
};