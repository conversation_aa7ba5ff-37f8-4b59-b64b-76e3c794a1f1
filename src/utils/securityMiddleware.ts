import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSession } from "@supabase/auth-helpers-react";
import { checkRateLimit } from "./rateLimit";
import { logAuditEvent } from "./auditLog";

export const useSecurityMiddleware = (
  requiredPermissions: string[] = [],
  rateLimitConfig?: { maxAttempts: number; windowMinutes: number }
) => {
  const session = useSession();
  const navigate = useNavigate();

  useEffect(() => {
    const checkSecurity = async () => {
      // Only redirect to auth if this is a protected route (indicated by requiredPermissions)
      if (requiredPermissions.length > 0 && !session?.user) {
        navigate("/auth");
        return;
      }

      if (session?.user && rateLimitConfig) {
        const isAllowed = await checkRateLimit(
          session.user.id,
          "page_access",
          rateLimitConfig.maxAttempts,
          rateLimitConfig.windowMinutes
        );

        if (!isAllowed) {
          navigate("/rate-limit-exceeded");
          return;
        }
      }

      // Only log access for authenticated users
      if (session?.user) {
        await logAuditEvent(
          session.user.id,
          "page_access",
          "page",
          window.location.pathname
        );
      }
    };

    checkSecurity();
  }, [session, navigate, rateLimitConfig, requiredPermissions]);

  return { session };
};