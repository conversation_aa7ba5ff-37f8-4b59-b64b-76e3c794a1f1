export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export const ALLOWED_FILE_TYPES = {
  'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  'document': ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
  'audio': ['audio/mpeg', 'audio/wav', 'audio/ogg']
} as const;

export const validateFile = (file: File, availableSpace: number) => {
  if (file.size > MAX_FILE_SIZE) {
    throw new Error("File size exceeds 50MB limit");
  }

  if (file.size > availableSpace) {
    throw new Error("Not enough storage space available");
  }
  
  const isValidType = Object.values(ALLOWED_FILE_TYPES).flat().includes(file.type);
  if (!isValidType) {
    throw new Error("File type not supported");
  }
  
  return true;
};