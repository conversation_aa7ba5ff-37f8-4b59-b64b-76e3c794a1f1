export const getBucketForFileType = (contentType: string) => {
  if (contentType.startsWith('image/')) return 'images';
  if (contentType.startsWith('audio/')) return 'audio';
  return 'documents';
};

export const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};