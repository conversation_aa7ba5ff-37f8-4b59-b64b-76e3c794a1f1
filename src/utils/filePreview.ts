import { FileRow } from "@/integrations/supabase/types/database";
import { supabase } from "@/integrations/supabase/client";

export const getFilePreview = async (file: FileRow): Promise<string | null> => {
  const bucket = getBucketForFileType(file.content_type);
  
  // Handle images
  if (file.content_type.startsWith('image/')) {
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(file.file_path);
    return publicUrl;
  }
  
  // Handle PDFs and text files
  if (file.content_type === 'application/pdf' || file.content_type.startsWith('text/')) {
    const { data } = await supabase.storage
      .from(bucket)
      .createSignedUrl(file.file_path, 3600);
    return data?.signedUrl || null;
  }
  
  // Handle audio files
  if (file.content_type.startsWith('audio/')) {
    const { data } = await supabase.storage
      .from(bucket)
      .createSignedUrl(file.file_path, 3600);
    return data?.signedUrl || null;
  }
  
  return null;
};

export const getBucketForFileType = (contentType: string) => {
  if (contentType.startsWith('image/')) return 'images';
  if (contentType.startsWith('audio/')) return 'audio';
  return 'documents';
};