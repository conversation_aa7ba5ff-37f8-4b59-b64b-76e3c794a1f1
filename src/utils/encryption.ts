import { supabase } from "@/integrations/supabase/client";

export const encryptFile = async (file: File): Promise<{ encryptedFile: Blob; key: string }> => {
  const key = await window.crypto.subtle.generateKey(
    { name: "AES-GCM", length: 256 },
    true,
    ["encrypt"]
  );

  const iv = window.crypto.getRandomValues(new Uint8Array(12));
  const arrayBuffer = await file.arrayBuffer();

  const encryptedContent = await window.crypto.subtle.encrypt(
    { name: "AES-GCM", iv },
    key,
    arrayBuffer
  );

  const exportedKey = await window.crypto.subtle.exportKey("raw", key);
  const keyString = btoa(String.fromCharCode(...new Uint8Array(exportedKey)));
  
  const encryptedBlob = new Blob([iv, encryptedContent], { type: file.type });

  return {
    encryptedFile: encryptedBlob,
    key: keyString,
  };
};

export const decryptFile = async (
  encryptedBlob: Blob,
  keyString: string
): Promise<Blob> => {
  const arrayBuffer = await encryptedBlob.arrayBuffer();
  const iv = arrayBuffer.slice(0, 12);
  const data = arrayBuffer.slice(12);

  const keyData = Uint8Array.from(atob(keyString), c => c.charCodeAt(0));
  const key = await window.crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "AES-GCM", length: 256 },
    false,
    ["decrypt"]
  );

  const decryptedContent = await window.crypto.subtle.decrypt(
    { name: "AES-GCM", iv: iv },
    key,
    data
  );

  return new Blob([decryptedContent], { type: encryptedBlob.type });
};