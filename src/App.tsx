import React, { useState, Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter } from "react-router-dom";
import { SessionContextProvider } from "@supabase/auth-helpers-react";
import { ThemeProvider } from "next-themes";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import Header from "@/components/Header";
import { SkipLink } from "@/components/ui/skip-link";
import { supabase } from "./integrations/supabase/client";
import { LoadingFallback } from "./components/LoadingFallback";
import { LazyRoutes } from "./components/LazyRoutes";

const App = () => {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <TooltipProvider>
          <SessionContextProvider supabaseClient={supabase}>
            <QueryClientProvider client={new QueryClient()}>
              <ErrorBoundary>
                <SkipLink />
                <Header />
                <main id="main" className="min-h-screen pt-14">
                  <Suspense fallback={<LoadingFallback />}>
                    <LazyRoutes />
                  </Suspense>
                </main>
                <Toaster />
                <Sonner />
              </ErrorBoundary>
            </QueryClientProvider>
          </SessionContextProvider>
        </TooltipProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

export default App;