export interface PricingFeature {
  name: string;
  included: boolean;
  description?: string;
}

export interface PricingPlan {
  name: string;
  price: string;
  description: string;
  features: PricingFeature[];
  popular?: boolean;
  storage?: string;
  messages?: string;
  aiUsage?: string;
  teamSize?: string;
}

export const pricingPlans: PricingPlan[] = [
  {
    name: "Free",
    price: "0",
    description: "Perfect for individual artists starting out",
    storage: "100MB",
    messages: "5/month",
    aiUsage: "3 matches/month",
    features: [
      { name: "Basic Profile", included: true, description: "Create your artist profile with essential information" },
      { name: "Basic AI Matching", included: true, description: "3 AI-powered matches per month" },
      { name: "Read-only Trends Access", included: true, description: "View industry trends and insights" },
      { name: "Basic EPK Generation", included: true, description: "Create simple electronic press kits" },
      { name: "AI Features", included: false, description: "Advanced AI-powered tools and features" },
      { name: "Analytics", included: false, description: "Track your performance and growth" }
    ]
  },
  {
    name: "Starter",
    price: "29",
    description: "For emerging artists ready to grow",
    storage: "2GB",
    messages: "30/month",
    aiUsage: "10 matches/month",
    features: [
      { name: "Enhanced Profile", included: true, description: "Add portfolio, media, and detailed information" },
      { name: "Basic AI Tools", included: true, description: "EPK generation and simple production advice" },
      { name: "Basic Analytics", included: true, description: "Track profile views and engagement" },
      { name: "Production Feedback", included: true, description: "Basic AI-powered mix analysis" },
      { name: "Trend Analysis", included: true, description: "Basic market trend insights" },
      { name: "Team Features", included: false, description: "Collaboration tools for teams" }
    ]
  },
  {
    name: "Growth",
    price: "99",
    description: "For professional artists and small labels",
    storage: "10GB",
    messages: "100/month",
    aiUsage: "Unlimited",
    teamSize: "Up to 3 members",
    popular: true,
    features: [
      { name: "Full Analytics Suite", included: true, description: "Comprehensive performance tracking" },
      { name: "Advanced AI Features", included: true, description: "Full access to AI-powered tools" },
      { name: "Cultural Collaboration", included: true, description: "AI-powered translation and collaboration" },
      { name: "Team Collaboration", included: true, description: "Tools for small teams" },
      { name: "Custom Branding", included: true, description: "Branded profiles and materials" },
      { name: "Revenue Tools", included: true, description: "Basic revenue tracking and optimization" }
    ]
  },
  {
    name: "Scale",
    price: "149",
    description: "For labels and venues",
    storage: "50GB",
    messages: "Unlimited",
    aiUsage: "Unlimited",
    teamSize: "Up to 10 members",
    features: [
      { name: "Advanced Team Features", included: true, description: "Extended team collaboration tools" },
      { name: "Venue/Label Tools", included: true, description: "Specialized features for businesses" },
      { name: "Priority Support", included: true, description: "Fast-track support response" },
      { name: "Revenue Optimization", included: true, description: "Advanced revenue tools and insights" },
      { name: "Custom Analytics", included: true, description: "Tailored reporting and insights" },
      { name: "API Access", included: false, description: "Access to our API for custom integration" }
    ]
  }
];