<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quincy.AI</title>
  <meta name="description" content="AI-Powered Music Industry Platform" />
  <meta name="author" content="Quincy.AI" />
  <meta property="og:image" content="/og-image.svg" />
  <meta name="theme-color" content="#FF3D00" />
  <link rel="manifest" href="/manifest.json" />
</head>
<body>
  <div id="root"></div>
  <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
  <script type="module" src="/src/main.tsx"></script>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => {
            console.log('ServiceWorker registration successful');
          })
          .catch(err => {
            console.log('ServiceWorker registration failed: ', err);
          });
      });
    }
  </script>
</body>
</html>